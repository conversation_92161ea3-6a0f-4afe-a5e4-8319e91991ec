// Registration Step 3: Creator Profile Submission
// Handles the third step of the multi-step registration process

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders, createCorsResponse, createCorsPreflightResponse } from '../_shared/cors.ts'
import { 
  validateRequired,
  validateFollowerCount,
  validateRevenueRange,
  combineValidationResults,
  ValidationResult 
} from '../_shared/validation.ts'

interface CreatorProfileRequest {
  primary_platform: string
  platform_username_handle: string
  follower_subscriber_count: string
  monthly_revenue: string
  content_category: string
  years_active_as_creator: string
}

interface ApiResponse {
  status: 'success' | 'error'
  message: string
  next_step?: string
  code?: string
  details?: any
}

// Validate platform username handle
function validatePlatformHandle(handle: string): ValidationResult {
  const errors: any[] = []
  
  if (!handle) {
    errors.push({
      field: 'platform_username_handle',
      reason: 'required',
      message: 'Platform username/handle is required'
    })
  } else if (handle.length < 2) {
    errors.push({
      field: 'platform_username_handle',
      reason: 'too_short',
      message: 'Platform handle must be at least 2 characters long'
    })
  } else if (handle.length > 50) {
    errors.push({
      field: 'platform_username_handle',
      reason: 'too_long',
      message: 'Platform handle must be less than 50 characters'
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Validate years active
function validateYearsActive(years: string): ValidationResult {
  const errors: any[] = []
  
  const validOptions = [
    'Less than 1 year', '1-2 years', '2-3 years', 
    '3-5 years', '5-10 years', '10+ years'
  ]
  
  if (!validOptions.includes(years)) {
    errors.push({
      field: 'years_active_as_creator',
      reason: 'invalid_value',
      message: 'Invalid years active option'
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Validate content category
function validateContentCategory(category: string): ValidationResult {
  const errors: any[] = []
  
  const validCategories = [
    'Education', 'Entertainment', 'Gaming', 'Lifestyle', 
    'Technology', 'Business', 'Health & Fitness', 'Travel',
    'Food & Cooking', 'Fashion & Beauty', 'Music', 'Art & Design',
    'Sports', 'News & Politics', 'Comedy', 'Other'
  ]
  
  if (!validCategories.includes(category)) {
    errors.push({
      field: 'content_category',
      reason: 'invalid_value',
      message: 'Invalid content category'
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return createCorsPreflightResponse()
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return createCorsResponse({
        status: 'error',
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only POST method is allowed'
      } as ApiResponse, 405)
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return createCorsResponse({
        status: 'error',
        code: 'UNAUTHORIZED',
        message: 'Authorization header is required'
      } as ApiResponse, 401)
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user from JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return createCorsResponse({
        status: 'error',
        code: 'INVALID_TOKEN',
        message: 'Invalid or expired token'
      } as ApiResponse, 401)
    }

    // Parse request body
    const body: CreatorProfileRequest = await req.json()

    // Validate all fields
    const validationResults: ValidationResult[] = [
      validateRequired(body.primary_platform, 'primary_platform'),
      validatePlatformHandle(body.platform_username_handle),
      validateFollowerCount(body.follower_subscriber_count),
      validateRevenueRange(body.monthly_revenue),
      validateContentCategory(body.content_category),
      validateYearsActive(body.years_active_as_creator)
    ]

    const combinedValidation = combineValidationResults(...validationResults)

    if (!combinedValidation.isValid) {
      return createCorsResponse({
        status: 'error',
        code: 'VALIDATION_ERROR',
        message: 'Validation failed',
        details: { errors: combinedValidation.errors }
      } as ApiResponse, 400)
    }

    // Check if user has creator role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role, metadata')
      .eq('id', user.id)
      .single()

    if (userError) {
      console.error('Error fetching user data:', userError)
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to fetch user information'
      } as ApiResponse, 500)
    }

    // Ensure user has creator role
    if (userData.role !== 'creator') {
      const { error: roleUpdateError } = await supabase
        .from('users')
        .update({ role: 'creator' })
        .eq('id', user.id)

      if (roleUpdateError) {
        console.error('Error updating user role:', roleUpdateError)
        return createCorsResponse({
          status: 'error',
          code: 'DATABASE_ERROR',
          message: 'Failed to update user role'
        } as ApiResponse, 500)
      }
    }

    // Insert or update creator profile
    const creatorData = {
      user_id: user.id,
      primary_platform: body.primary_platform,
      platform_username_handle: body.platform_username_handle,
      follower_subscriber_count: body.follower_subscriber_count,
      monthly_revenue: body.monthly_revenue,
      content_category: body.content_category,
      years_active_as_creator: body.years_active_as_creator,
      verification_status: 'pending'
    }

    const { error: creatorError } = await supabase
      .from('creators')
      .upsert(creatorData)

    if (creatorError) {
      console.error('Error saving creator profile:', creatorError)
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to save creator profile'
      } as ApiResponse, 500)
    }

    // Update user metadata
    const { error: metadataError } = await supabase
      .from('users')
      .update({
        metadata: {
          ...userData.metadata || {},
          registration_step: 3,
          registration_completed_steps: [
            ...(userData.metadata?.registration_completed_steps || []),
            'creator_profile'
          ]
        }
      })
      .eq('id', user.id)

    if (metadataError) {
      console.error('Error updating user metadata:', metadataError)
      // Don't fail the request for metadata update errors
    }

    // Send notification about profile creation
    const { error: notificationError } = await supabase
      .rpc('send_notification', {
        p_user_id: user.id,
        p_type: 'in_app',
        p_subject: 'Creator Profile Created',
        p_body: 'Your creator profile has been successfully created and is pending verification.',
        p_metadata: { step: 'creator_profile' }
      })

    if (notificationError) {
      console.error('Error sending notification:', notificationError)
      // Don't fail the request for notification errors
    }

    // Return success response
    return createCorsResponse({
      status: 'success',
      message: 'Creator profile saved successfully.',
      next_step: 'document_verification'
    } as ApiResponse)

  } catch (error) {
    console.error('Unexpected error in register-step-3:', error)
    return createCorsResponse({
      status: 'error',
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred'
    } as ApiResponse, 500)
  }
})
