// Registration Step 1: Referral Information Submission
// Handles the first step of the multi-step registration process

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface ReferralRequest {
  invite_code: string
  referrer_full_name: string
  relationship_to_referrer: string
  how_met_referrer: string
}

interface ApiResponse {
  status: 'success' | 'error'
  message: string
  next_step?: string
  code?: string
  details?: any
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({
          status: 'error',
          code: 'METHOD_NOT_ALLOWED',
          message: 'Only POST method is allowed'
        } as ApiResponse),
        {
          status: 405,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({
          status: 'error',
          code: 'UNAUTHORIZED',
          message: 'Authorization header is required'
        } as ApiResponse),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user from JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return new Response(
        JSON.stringify({
          status: 'error',
          code: 'INVALID_TOKEN',
          message: 'Invalid or expired token'
        } as ApiResponse),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body
    const body: ReferralRequest = await req.json()

    // Validate required fields
    const requiredFields = ['invite_code', 'referrer_full_name', 'relationship_to_referrer', 'how_met_referrer']
    const missingFields = requiredFields.filter(field => !body[field as keyof ReferralRequest])

    if (missingFields.length > 0) {
      return new Response(
        JSON.stringify({
          status: 'error',
          code: 'VALIDATION_ERROR',
          message: `Missing required fields: ${missingFields.join(', ')}`,
          details: { missing_fields: missingFields }
        } as ApiResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Validate invite code format (5 digits as per example)
    if (!/^\d{5}$/.test(body.invite_code)) {
      return new Response(
        JSON.stringify({
          status: 'error',
          code: 'INVALID_INVITE_CODE',
          message: 'Invite code must be exactly 5 digits'
        } as ApiResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // TODO: Validate invite code against database or predefined list
    // For now, we'll accept any 5-digit code except "00000" which might be a test code
    const validInviteCodes = ['12345', '54321', '11111', '99999'] // Example valid codes
    if (!validInviteCodes.includes(body.invite_code)) {
      return new Response(
        JSON.stringify({
          status: 'error',
          code: 'INVALID_INVITE_CODE',
          message: 'The provided invite code is invalid or expired'
        } as ApiResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Find the referrer by invite code (this would be more sophisticated in production)
    // For now, we'll create a referral record with the provided information
    
    // Generate a unique referral code for tracking
    const { data: referralCodeData, error: referralCodeError } = await supabase
      .rpc('generate_referral_code')

    if (referralCodeError) {
      console.error('Error generating referral code:', referralCodeError)
      return new Response(
        JSON.stringify({
          status: 'error',
          code: 'INTERNAL_ERROR',
          message: 'Failed to generate referral code'
        } as ApiResponse),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Store referral information
    const { error: referralError } = await supabase
      .from('referrals')
      .insert({
        referrer_id: user.id, // This would be the actual referrer's ID in production
        referred_user_id: user.id,
        referral_code: referralCodeData,
        invite_code: body.invite_code,
        referrer_full_name: body.referrer_full_name,
        relationship_to_referrer: body.relationship_to_referrer,
        how_met_referrer: body.how_met_referrer,
        status: 'pending'
      })

    if (referralError) {
      console.error('Error storing referral information:', referralError)
      return new Response(
        JSON.stringify({
          status: 'error',
          code: 'DATABASE_ERROR',
          message: 'Failed to store referral information'
        } as ApiResponse),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Update user metadata to track registration progress
    const { error: updateError } = await supabase
      .from('users')
      .update({
        metadata: {
          registration_step: 1,
          registration_completed_steps: ['referral'],
          referral_code: referralCodeData
        }
      })
      .eq('id', user.id)

    if (updateError) {
      console.error('Error updating user metadata:', updateError)
      // Don't fail the request for metadata update errors
    }

    // Return success response
    return new Response(
      JSON.stringify({
        status: 'success',
        message: 'Referral information saved successfully.',
        next_step: 'personal_information'
      } as ApiResponse),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Unexpected error in register-step-1:', error)
    return new Response(
      JSON.stringify({
        status: 'error',
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred'
      } as ApiResponse),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
