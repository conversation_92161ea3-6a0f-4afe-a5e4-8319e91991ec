-- Storage Setup for 360 Creator Bank App
-- This migration sets up storage buckets and policies for file uploads

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  (
    'kyc-documents',
    'kyc-documents',
    false, -- Private bucket for sensitive documents
    ********, -- 10MB limit
    ARRAY[
      'image/jpeg',
      'image/jpg',
      'image/png', 
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
  ),
  (
    'profile-pictures',
    'profile-pictures', 
    true, -- Public bucket for profile pictures
    5242880, -- 5MB limit
    ARRAY[
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp'
    ]
  ),
  (
    'creator-content',
    'creator-content',
    true, -- Public bucket for creator portfolio content
    ********, -- 50MB limit
    ARRAY[
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/webp',
      'video/mp4',
      'video/webm',
      'audio/mpeg',
      'audio/wav',
      'application/pdf'
    ]
  )
ON CONFLICT (id) DO NOTHING;

-- Storage policies for KYC documents bucket
CREATE POLICY "Users can upload their own KYC documents" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'kyc-documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view their own KYC documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'kyc-documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own KYC documents" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'kyc-documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own KYC documents" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'kyc-documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Admins can view all KYC documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'kyc-documents' AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Managers can view all KYC documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'kyc-documents' AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
  );

-- Storage policies for profile pictures bucket
CREATE POLICY "Users can upload their own profile pictures" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'profile-pictures' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Anyone can view profile pictures" ON storage.objects
  FOR SELECT USING (bucket_id = 'profile-pictures');

CREATE POLICY "Users can update their own profile pictures" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'profile-pictures' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own profile pictures" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'profile-pictures' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Storage policies for creator content bucket
CREATE POLICY "Creators can upload their own content" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'creator-content' AND
    auth.uid()::text = (storage.foldername(name))[1] AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'creator'
    )
  );

CREATE POLICY "Anyone can view creator content" ON storage.objects
  FOR SELECT USING (bucket_id = 'creator-content');

CREATE POLICY "Creators can update their own content" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'creator-content' AND
    auth.uid()::text = (storage.foldername(name))[1] AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'creator'
    )
  );

CREATE POLICY "Creators can delete their own content" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'creator-content' AND
    auth.uid()::text = (storage.foldername(name))[1] AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'creator'
    )
  );

-- Function to clean up orphaned files
CREATE OR REPLACE FUNCTION public.cleanup_orphaned_files()
RETURNS void AS $$
DECLARE
    file_record RECORD;
BEGIN
    -- Clean up KYC documents that are not referenced in kyc_documents table
    FOR file_record IN 
        SELECT name FROM storage.objects 
        WHERE bucket_id = 'kyc-documents'
        AND name NOT IN (
            SELECT SUBSTRING(document_url FROM '[^/]+$') 
            FROM public.kyc_documents 
            WHERE document_url IS NOT NULL
        )
    LOOP
        DELETE FROM storage.objects 
        WHERE bucket_id = 'kyc-documents' AND name = file_record.name;
    END LOOP;
    
    -- Clean up profile pictures that are not referenced in users table
    FOR file_record IN 
        SELECT name FROM storage.objects 
        WHERE bucket_id = 'profile-pictures'
        AND name NOT IN (
            SELECT SUBSTRING(profile_picture FROM '[^/]+$') 
            FROM public.users 
            WHERE profile_picture IS NOT NULL
        )
    LOOP
        DELETE FROM storage.objects 
        WHERE bucket_id = 'profile-pictures' AND name = file_record.name;
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get file info
CREATE OR REPLACE FUNCTION public.get_file_info(bucket_name TEXT, file_path TEXT)
RETURNS JSON AS $$
DECLARE
    file_info JSON;
BEGIN
    SELECT json_build_object(
        'name', name,
        'bucket_id', bucket_id,
        'size', metadata->>'size',
        'mimetype', metadata->>'mimetype',
        'created_at', created_at,
        'updated_at', updated_at
    ) INTO file_info
    FROM storage.objects
    WHERE bucket_id = bucket_name AND name = file_path;
    
    RETURN file_info;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate file upload
CREATE OR REPLACE FUNCTION public.validate_file_upload(
    bucket_name TEXT,
    file_name TEXT,
    file_size BIGINT,
    mime_type TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    bucket_config RECORD;
BEGIN
    -- Get bucket configuration
    SELECT * INTO bucket_config
    FROM storage.buckets
    WHERE id = bucket_name;
    
    IF bucket_config IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Check file size limit
    IF file_size > bucket_config.file_size_limit THEN
        RETURN FALSE;
    END IF;
    
    -- Check allowed mime types
    IF bucket_config.allowed_mime_types IS NOT NULL AND 
       NOT (mime_type = ANY(bucket_config.allowed_mime_types)) THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions for storage functions
GRANT EXECUTE ON FUNCTION public.cleanup_orphaned_files() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_file_info(TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.validate_file_upload(TEXT, TEXT, BIGINT, TEXT) TO authenticated;

-- Create indexes for better storage query performance
CREATE INDEX IF NOT EXISTS idx_storage_objects_bucket_user ON storage.objects(bucket_id, (storage.foldername(name))[1]);
CREATE INDEX IF NOT EXISTS idx_storage_objects_created_at ON storage.objects(created_at);

-- Add helpful comments
COMMENT ON FUNCTION public.cleanup_orphaned_files() IS 'Removes storage files that are no longer referenced in the database';
COMMENT ON FUNCTION public.get_file_info(TEXT, TEXT) IS 'Returns metadata information for a specific file';
COMMENT ON FUNCTION public.validate_file_upload(TEXT, TEXT, BIGINT, TEXT) IS 'Validates file upload against bucket constraints';
