# 360 Creator Bank Backend

A complete Backend-as-a-Service (BaaS) implementation using Supabase for the 360 Creator Bank App.

## Architecture Overview

This backend is built entirely on Supabase's native services:

- **Database**: PostgreSQL with comprehensive schema for users, creators, investors, deals, and transactions
- **Authentication**: Supabase Auth with role-based access control
- **API**: Auto-generated REST and GraphQL APIs
- **Business Logic**: Supabase Edge Functions (Deno-based serverless functions)
- **File Storage**: Supabase Storage for document uploads and KYC verification
- **Real-time**: Supabase Realtime for live updates
- **Security**: Row Level Security (RLS) policies for data protection

## Features

### Phase 1: Core Infrastructure
- ✅ User authentication and registration
- ✅ Role-based access control (Creator, Investor, Admin)
- ✅ KYC document management
- ✅ User profile management

### Phase 2: Registration API (In Progress)
- 🔄 Multi-step registration process
- 🔄 Document verification system
- 🔄 Social verification
- 🔄 Referral system integration

## Getting Started

### Prerequisites
- Node.js 18+ (for Supabase CLI)
- Supabase CLI installed globally
- Supabase account

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd 360-creator-bank-backend
```

2. Install Supabase CLI:
```bash
npm install -g supabase
```

3. Initialize Supabase project:
```bash
supabase init
```

4. Start local development:
```bash
supabase start
```

## Project Structure

```
360-creator-bank-backend/
├── supabase/
│   ├── config.toml              # Supabase configuration
│   ├── seed.sql                 # Database seed data
│   ├── migrations/              # Database migrations
│   │   ├── 001_initial_schema.sql
│   │   ├── 002_rls_policies.sql
│   │   └── 003_functions_triggers.sql
│   └── functions/               # Edge Functions
│       ├── registration/        # Registration-related functions
│       ├── auth/               # Authentication helpers
│       └── utils/              # Shared utilities
├── docs/                       # API documentation
├── tests/                      # Test files
└── README.md
```

## Database Schema

The database follows the comprehensive schema defined in the requirements:

- **users**: Core user information and authentication
- **creators**: Creator-specific profile data
- **investors**: Investor-specific profile data
- **deals**: Investment opportunities and funding rounds
- **investments**: Individual investment records
- **transactions**: Financial transaction logs
- **notifications**: System notifications
- **referrals**: Referral tracking system

## API Endpoints

All API endpoints are auto-generated by Supabase and accessible via:

- **REST API**: `https://your-project.supabase.co/rest/v1/`
- **GraphQL**: `https://your-project.supabase.co/graphql/v1`

Custom business logic is implemented through Edge Functions:

- `/functions/v1/register-step-1` - Referral information
- `/functions/v1/register-step-2` - Personal information
- `/functions/v1/register-step-3` - Creator profile
- `/functions/v1/register-step-4` - Document verification
- `/functions/v1/register-step-5` - Social verification

## Security

Security is implemented through multiple layers:

1. **Row Level Security (RLS)**: Database-level access control
2. **Authentication**: JWT-based user authentication
3. **Authorization**: Role-based permissions
4. **Input Validation**: Server-side validation in Edge Functions
5. **Rate Limiting**: API protection through Edge Functions

## Development

### Running Locally

```bash
# Start Supabase services
supabase start

# Apply migrations
supabase db reset

# Deploy Edge Functions
supabase functions deploy

# View logs
supabase functions logs
```

### Testing

```bash
# Run database tests
supabase test db

# Test Edge Functions
supabase functions serve
```

## Deployment

The backend is deployed automatically through Supabase's managed infrastructure. No additional server setup required.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes following the established patterns
4. Test thoroughly
5. Submit a pull request

## License

[License information]
