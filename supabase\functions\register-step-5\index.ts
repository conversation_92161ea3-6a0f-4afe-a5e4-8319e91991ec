// Registration Step 5: Social Verification
// Handles the fifth and final step of the multi-step registration process

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders, createCorsResponse, createCorsPreflightResponse } from '../_shared/cors.ts'
import { validateUrl, combineValidationResults, ValidationResult } from '../_shared/validation.ts'

interface SocialVerificationRequest {
  linkedin_profile?: string
  personal_website?: string
  portfolio_links?: string[]
}

interface ApiResponse {
  status: 'success' | 'error'
  message: string
  next_step?: string
  code?: string
  details?: any
}

// Validate LinkedIn profile URL
function validateLinkedInUrl(url: string): ValidationResult {
  const errors: any[] = []
  
  if (url && !url.includes('linkedin.com')) {
    errors.push({
      field: 'linkedin_profile',
      reason: 'invalid_domain',
      message: 'LinkedIn profile must be a valid LinkedIn URL'
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Validate portfolio links array
function validatePortfolioLinks(links: string[]): ValidationResult {
  const errors: any[] = []
  
  if (links && links.length > 5) {
    errors.push({
      field: 'portfolio_links',
      reason: 'too_many',
      message: 'Maximum 5 portfolio links allowed'
    })
  }
  
  // Validate each URL in the array
  if (links) {
    links.forEach((link, index) => {
      const urlValidation = validateUrl(link, `portfolio_links[${index}]`, false)
      if (!urlValidation.isValid) {
        errors.push(...urlValidation.errors)
      }
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return createCorsPreflightResponse()
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return createCorsResponse({
        status: 'error',
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only POST method is allowed'
      } as ApiResponse, 405)
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return createCorsResponse({
        status: 'error',
        code: 'UNAUTHORIZED',
        message: 'Authorization header is required'
      } as ApiResponse, 401)
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user from JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return createCorsResponse({
        status: 'error',
        code: 'INVALID_TOKEN',
        message: 'Invalid or expired token'
      } as ApiResponse, 401)
    }

    // Parse request body
    const body: SocialVerificationRequest = await req.json()

    // Validate all fields (all are optional)
    const validationResults: ValidationResult[] = []

    if (body.linkedin_profile) {
      validationResults.push(validateUrl(body.linkedin_profile, 'linkedin_profile', false))
      validationResults.push(validateLinkedInUrl(body.linkedin_profile))
    }

    if (body.personal_website) {
      validationResults.push(validateUrl(body.personal_website, 'personal_website', false))
    }

    if (body.portfolio_links) {
      validationResults.push(validatePortfolioLinks(body.portfolio_links))
    }

    const combinedValidation = combineValidationResults(...validationResults)

    if (!combinedValidation.isValid) {
      return createCorsResponse({
        status: 'error',
        code: 'VALIDATION_ERROR',
        message: 'Social verification validation failed',
        details: { errors: combinedValidation.errors }
      } as ApiResponse, 400)
    }

    // Get current user and creator data
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('metadata')
      .eq('id', user.id)
      .single()

    if (userError) {
      console.error('Error fetching user data:', userError)
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to fetch user information'
      } as ApiResponse, 500)
    }

    // Prepare social links object
    const socialLinks = {
      linkedin: body.linkedin_profile || null,
      website: body.personal_website || null,
      portfolio: body.portfolio_links || []
    }

    // Update creator profile with social links
    const { error: creatorUpdateError } = await supabase
      .from('creators')
      .update({
        social_links: socialLinks,
        portfolio_url: body.personal_website || null
      })
      .eq('user_id', user.id)

    if (creatorUpdateError) {
      console.error('Error updating creator social links:', creatorUpdateError)
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to save social verification information'
      } as ApiResponse, 500)
    }

    // Update user metadata to mark registration as complete
    const { error: metadataError } = await supabase
      .from('users')
      .update({
        metadata: {
          ...userData.metadata || {},
          registration_step: 5,
          registration_completed_steps: [
            ...(userData.metadata?.registration_completed_steps || []),
            'social_verification'
          ],
          registration_completed: true,
          registration_completed_at: new Date().toISOString()
        }
      })
      .eq('id', user.id)

    if (metadataError) {
      console.error('Error updating user metadata:', metadataError)
      // Don't fail the request for metadata update errors
    }

    // Send welcome notification
    const { error: notificationError } = await supabase
      .rpc('send_notification', {
        p_user_id: user.id,
        p_type: 'email',
        p_subject: 'Registration Complete - Welcome to 360 Creator Bank!',
        p_body: 'Congratulations! Your registration is now complete. Your account is pending final approval. We will notify you once your account is fully activated.',
        p_metadata: { 
          step: 'registration_complete',
          completed_steps: userData.metadata?.registration_completed_steps?.length || 0
        }
      })

    if (notificationError) {
      console.error('Error sending welcome notification:', notificationError)
      // Don't fail the request for notification errors
    }

    // Return success response
    return createCorsResponse({
      status: 'success',
      message: 'Social verification links saved successfully.',
      next_step: 'registration_complete'
    } as ApiResponse)

  } catch (error) {
    console.error('Unexpected error in register-step-5:', error)
    return createCorsResponse({
      status: 'error',
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred'
    } as ApiResponse, 500)
  }
})
