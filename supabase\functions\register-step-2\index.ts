// Registration Step 2: Personal Information Submission
// Handles the second step of the multi-step registration process

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders, createCorsResponse, createCorsPreflightResponse } from '../_shared/cors.ts'
import { 
  validateEmail, 
  validatePhoneNumber, 
  validateDateOfBirth, 
  validateName, 
  validatePostalCode,
  validateRequired,
  combineValidationResults,
  ValidationResult 
} from '../_shared/validation.ts'

interface PersonalInfoRequest {
  first_name: string
  last_name: string
  date_of_birth: string
  phone_number: string
  address: string
  city: string
  country: string
  postal_code: string
}

interface ApiResponse {
  status: 'success' | 'error'
  message: string
  next_step?: string
  code?: string
  details?: any
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return createCorsPreflightResponse()
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return createCorsResponse({
        status: 'error',
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only POST method is allowed'
      } as ApiResponse, 405)
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return createCorsResponse({
        status: 'error',
        code: 'UNAUTHORIZED',
        message: 'Authorization header is required'
      } as ApiResponse, 401)
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user from JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return createCorsResponse({
        status: 'error',
        code: 'INVALID_TOKEN',
        message: 'Invalid or expired token'
      } as ApiResponse, 401)
    }

    // Parse request body
    const body: PersonalInfoRequest = await req.json()

    // Validate all fields
    const validationResults: ValidationResult[] = [
      validateName(body.first_name, 'first_name'),
      validateName(body.last_name, 'last_name'),
      validateDateOfBirth(body.date_of_birth),
      validatePhoneNumber(body.phone_number),
      validateRequired(body.address, 'address'),
      validateRequired(body.city, 'city'),
      validateRequired(body.country, 'country'),
      validatePostalCode(body.postal_code, body.country)
    ]

    const combinedValidation = combineValidationResults(...validationResults)

    if (!combinedValidation.isValid) {
      return createCorsResponse({
        status: 'error',
        code: 'VALIDATION_ERROR',
        message: 'Validation failed',
        details: { errors: combinedValidation.errors }
      } as ApiResponse, 400)
    }

    // Check if user already exists in users table
    const { data: existingUser, error: userCheckError } = await supabase
      .from('users')
      .select('id, metadata')
      .eq('id', user.id)
      .single()

    if (userCheckError && userCheckError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking existing user:', userCheckError)
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to check user status'
      } as ApiResponse, 500)
    }

    // Prepare address object
    const addressObject = {
      street: body.address,
      city: body.city,
      country: body.country,
      postal_code: body.postal_code
    }

    // Update user information
    const updateData = {
      first_name: body.first_name,
      last_name: body.last_name,
      phone_number: body.phone_number,
      date_of_birth: body.date_of_birth,
      address: addressObject,
      metadata: {
        ...existingUser?.metadata || {},
        registration_step: 2,
        registration_completed_steps: [
          ...(existingUser?.metadata?.registration_completed_steps || []),
          'personal_information'
        ]
      }
    }

    const { error: updateError } = await supabase
      .from('users')
      .upsert(updateData)
      .eq('id', user.id)

    if (updateError) {
      console.error('Error updating user information:', updateError)
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to save personal information'
      } as ApiResponse, 500)
    }

    // Send notification about profile update
    const { error: notificationError } = await supabase
      .rpc('send_notification', {
        p_user_id: user.id,
        p_type: 'in_app',
        p_subject: 'Personal Information Updated',
        p_body: 'Your personal information has been successfully updated.',
        p_metadata: { step: 'personal_information' }
      })

    if (notificationError) {
      console.error('Error sending notification:', notificationError)
      // Don't fail the request for notification errors
    }

    // Return success response
    return createCorsResponse({
      status: 'success',
      message: 'Personal information saved successfully.',
      next_step: 'creator_profile'
    } as ApiResponse)

  } catch (error) {
    console.error('Unexpected error in register-step-2:', error)
    return createCorsResponse({
      status: 'error',
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred'
    } as ApiResponse, 500)
  }
})
