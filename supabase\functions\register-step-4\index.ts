// Registration Step 4: Document Verification
// Handles the fourth step of the multi-step registration process

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders, createCorsResponse, createCorsPreflightResponse } from '../_shared/cors.ts'
import { validateUrl, validateRequired, combineValidationResults, ValidationResult } from '../_shared/validation.ts'

interface DocumentVerificationRequest {
  government_id_url: string
  proof_of_income_url: string
  platform_verification_url: string
  business_documents_url?: string
}

interface ApiResponse {
  status: 'success' | 'error'
  message: string
  next_step?: string
  code?: string
  details?: any
}

// Validate document URL and check if it's from Supabase Storage
function validateDocumentUrl(url: string, fieldName: string, required: boolean = true): ValidationResult {
  const errors: any[] = []
  
  if (!url) {
    if (required) {
      errors.push({
        field: fieldName,
        reason: 'required',
        message: `${fieldName.replace('_', ' ')} is required`
      })
    }
    return { isValid: !required, errors }
  }
  
  // Basic URL validation
  const urlValidation = validateUrl(url, fieldName, required)
  if (!urlValidation.isValid) {
    return urlValidation
  }
  
  // Check if URL is from Supabase Storage (basic check)
  const supabaseUrl = Deno.env.get('SUPABASE_URL')
  if (supabaseUrl && !url.includes(supabaseUrl)) {
    errors.push({
      field: fieldName,
      reason: 'invalid_source',
      message: 'Document must be uploaded to the platform storage'
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return createCorsPreflightResponse()
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return createCorsResponse({
        status: 'error',
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only POST method is allowed'
      } as ApiResponse, 405)
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return createCorsResponse({
        status: 'error',
        code: 'UNAUTHORIZED',
        message: 'Authorization header is required'
      } as ApiResponse, 401)
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user from JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return createCorsResponse({
        status: 'error',
        code: 'INVALID_TOKEN',
        message: 'Invalid or expired token'
      } as ApiResponse, 401)
    }

    // Parse request body
    const body: DocumentVerificationRequest = await req.json()

    // Validate all document URLs
    const validationResults: ValidationResult[] = [
      validateDocumentUrl(body.government_id_url, 'government_id_url', true),
      validateDocumentUrl(body.proof_of_income_url, 'proof_of_income_url', true),
      validateDocumentUrl(body.platform_verification_url, 'platform_verification_url', true),
      validateDocumentUrl(body.business_documents_url || '', 'business_documents_url', false)
    ]

    const combinedValidation = combineValidationResults(...validationResults)

    if (!combinedValidation.isValid) {
      return createCorsResponse({
        status: 'error',
        code: 'VALIDATION_ERROR',
        message: 'Document validation failed',
        details: { errors: combinedValidation.errors }
      } as ApiResponse, 400)
    }

    // Get user metadata
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('metadata')
      .eq('id', user.id)
      .single()

    if (userError) {
      console.error('Error fetching user data:', userError)
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to fetch user information'
      } as ApiResponse, 500)
    }

    // Prepare document records
    const documents = [
      {
        user_id: user.id,
        document_type: 'government_id',
        document_url: body.government_id_url,
        file_name: body.government_id_url.split('/').pop() || 'government_id',
        status: 'pending'
      },
      {
        user_id: user.id,
        document_type: 'proof_of_income',
        document_url: body.proof_of_income_url,
        file_name: body.proof_of_income_url.split('/').pop() || 'proof_of_income',
        status: 'pending'
      },
      {
        user_id: user.id,
        document_type: 'platform_verification',
        document_url: body.platform_verification_url,
        file_name: body.platform_verification_url.split('/').pop() || 'platform_verification',
        status: 'pending'
      }
    ]

    // Add business documents if provided
    if (body.business_documents_url) {
      documents.push({
        user_id: user.id,
        document_type: 'business_documents',
        document_url: body.business_documents_url,
        file_name: body.business_documents_url.split('/').pop() || 'business_documents',
        status: 'pending'
      })
    }

    // Insert document records
    const { error: documentsError } = await supabase
      .from('kyc_documents')
      .upsert(documents, { onConflict: 'user_id,document_type' })

    if (documentsError) {
      console.error('Error saving document records:', documentsError)
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to save document information'
      } as ApiResponse, 500)
    }

    // Update user KYC status to under_review
    const { error: kycUpdateError } = await supabase
      .from('users')
      .update({ 
        kyc_status: 'under_review',
        metadata: {
          ...userData.metadata || {},
          registration_step: 4,
          registration_completed_steps: [
            ...(userData.metadata?.registration_completed_steps || []),
            'document_verification'
          ]
        }
      })
      .eq('id', user.id)

    if (kycUpdateError) {
      console.error('Error updating KYC status:', kycUpdateError)
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to update verification status'
      } as ApiResponse, 500)
    }

    // Send notification about document submission
    const { error: notificationError } = await supabase
      .rpc('send_notification', {
        p_user_id: user.id,
        p_type: 'email',
        p_subject: 'Documents Submitted for Review',
        p_body: 'Your KYC documents have been submitted and are now under review. We will notify you once the review is complete.',
        p_metadata: { step: 'document_verification', documents_count: documents.length }
      })

    if (notificationError) {
      console.error('Error sending notification:', notificationError)
      // Don't fail the request for notification errors
    }

    // Return success response
    return createCorsResponse({
      status: 'success',
      message: 'Documents uploaded successfully. Awaiting review.',
      next_step: 'social_verification'
    } as ApiResponse)

  } catch (error) {
    console.error('Unexpected error in register-step-4:', error)
    return createCorsResponse({
      status: 'error',
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred'
    } as ApiResponse, 500)
  }
})
