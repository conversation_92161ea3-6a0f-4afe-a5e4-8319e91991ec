# Registration API Documentation for 360 Creator Bank App

This document outlines the API endpoints and payloads for the multi-step registration process of the 360 Creator Bank App. It is designed to be easily understandable by an AI for step-by-step backend implementation.

## Overview

The registration process consists of several steps, each requiring specific user input. This document defines the API calls for submitting data at each stage. It is assumed that a session token or similar authentication mechanism will be used to maintain state across these steps, likely obtained after an initial signup or invite code validation.

## API Base URL

`https://api.360creatorbank.com/v1` (Example)

## Step 1: Referral Information Submission

This step captures information about how the user was referred to the platform.

### Endpoint

`POST  /functions/v1/register-step-1`

### Description

Submits the referral details provided by the user. This is typically the first step after an initial invite code validation (if not handled by the invite code endpoint itself).

### Request Body (JSON)

```json
{
  "invite_code": "00000",
  "referrer_full_name": "tres",
  "relationship_to_referrer": "Colleague",
  "how_met_referrer": "glggjh"
}
```

### Response Body (JSON - Success)

```json
{
  "status": "success",
  "message": "Referral information saved successfully.",
  "next_step": "personal_information"
}
```

### Response Body (JSON - Error Example)

```json
{

  "status": "error",
  "code": "INVALID_INVITE_CODE",
  "message": "The provided invite code is invalid or expired."
}
```

### Business Logic
- Validate the `invite_code` against a predefined list or database.
- Store referral information linked to the user's session or temporary registration record.
- If the invite code is invalid, return an appropriate error.

## Step 2: Personal Information Submission

This step collects basic personal details for identity verification.

### Endpoint

`POST /functions/v1/register-step-2`

### Description

Submits the user's personal details. This information is crucial for KYC (Know Your Customer) processes.

### Request Body (JSON)

```json
{
  "first_name": "John",
  "last_name": "Doe",
  "date_of_birth": "1990-01-15",
  "phone_number": "+***********",
  "address": "123 Main St",
  "city": "Anytown",
  "country": "USA",
  "postal_code": "12345"
}
```

### Response Body (JSON - Success)

```json
{
  "status": "success",
  "message": "Personal information saved successfully.",
  "next_step": "creator_profile"
}
```

### Response Body (JSON - Error Example)

```json
{
  "status": "error",
  "code": "VALIDATION_ERROR",
  "message": "Invalid date of birth format.",
  "details": {
    "field": "date_of_birth",
    "reason": "invalid_format"
  }
}
```

### Business Logic
- Validate all fields for correct format and completeness.
- Store personal information in the `users` table or a temporary staging area.
- If 


the user is a creator, update the `creators` table with relevant details.

## Step 3: Creator Profile Submission

This step gathers details about the creator's business and platform presence.

### Endpoint

`POST /functions/v1/register-step-`

### Description

Submits the creator-specific profile details. This endpoint is typically called after personal information has been provided.

### Request Body (JSON)

```json
{
  "primary_platform": "Instagram",
  "platform_username_handle": "@test",
  "follower_subscriber_count": "50K - 100K",
  "monthly_revenue": "$10K - $25K",
  "content_category": "Education",
  "years_active_as_creator": "1-2 years"
}
```

### Response Body (JSON - Success)

```json
{
  "status": "success",
  "message": "Creator profile saved successfully.",
  "next_step": "document_verification"
}
```

### Response Body (JSON - Error Example)

```json
{
  "status": "error",
  "code": "VALIDATION_ERROR",
  "message": "Invalid follower count format.",
  "details": {
    "field": "follower_subscriber_count",
    "reason": "invalid_value"
  }
}
```

### Business Logic
- Validate all fields for correct format and completeness.
- Store creator profile information in the `creators` table, linked to the user.

## Step 4: Document Verification

This step requires the user to upload various documents for identity and income verification.

### Endpoint

`POST /register/document-verification`

### Description

Uploads documents required for KYC and income verification. This endpoint will likely handle file uploads, storing them in Supabase Storage and recording their URLs.

### Request Body (JSON)

```json
{
  "government_id_url": "https://your-supabase-storage-url/government_id.png",
  "proof_of_income_url": "https://your-supabase-storage-url/proof_of_income.pdf",
  "platform_verification_url": "https://your-supabase-storage-url/platform_verification.png",
  "business_documents_url": "https://your-supabase-storage-url/business_documents.pdf" (Optional)
}
```

### Response Body (JSON - Success)

```json
{
  "status": "success",
  "message": "Documents uploaded successfully. Awaiting review.",
  "next_step": "social_verification"
}
```

### Response Body (JSON - Error Example)

```json
{
  "status": "error",
  "code": "UPLOAD_FAILED",
  "message": "Failed to upload government ID. Please try again."
}
```

### Business Logic
- Securely handle file uploads to Supabase Storage.
- Store the URLs of the uploaded documents in the user's KYC record or a dedicated `documents` table.
- Update the user's `kyc_status` to indicate documents are submitted for review.

## Step 5: Social Verification

This step collects information about the user's online presence for additional verification.

### Endpoint

`POST /register/social-verification`

### Description

Submits links to the user's social media profiles and personal websites for verification.

### Request Body (JSON)

```json
{
  "linkedin_profile": "https://linkedin.com/in/yourprofile",
  "personal_website": "https://yourwebsite.com",
  "portfolio_links": [
    "https://yourportfolio.com/link1",
    "https://yourportfolio.com/link2",
    "https://yourportfolio.com/link3"
  ]
}
```

### Response Body (JSON - Success)

```json
{
  "status": "success",
  "message": "Social verification links saved successfully.",
  "next_step": "registration_complete"
}
```

### Response Body (JSON - Error Example)

```json
{
  "status": "error",
  "code": "INVALID_URL",
  "message": "The provided LinkedIn profile URL is invalid.",
  "details": {
    "field": "linkedin_profile",
    "reason": "invalid_format"
  }
}
```

### Business Logic
- Validate all provided URLs for correct format.
- Store social verification links in the user's profile or a dedicated table.

## Final Step: Registration Completion

After all steps are successfully completed, the user's registration is finalized.

### Endpoint

`POST /register/complete`

### Description

Marks the user's registration as complete. This endpoint might trigger final setup processes or welcome emails.

### Request Body (JSON)

```json
{}
```

### Response Body (JSON - Success)

```json
{
  "status": "success",
  "message": "Registration complete. Welcome to 360 Creator Bank App!"
}
```

### Business Logic
- Update the user's status to 'active' or 'registered'.
- Trigger welcome email and other onboarding processes.

This detailed API documentation, broken down by registration step and including example payloads, should enable an AI to easily understand and implement the backend for the 360 Creator Bank App's registration process using Supabase.

