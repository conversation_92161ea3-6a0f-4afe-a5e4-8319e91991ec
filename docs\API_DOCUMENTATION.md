# 360 Creator Bank API Documentation

## Overview

The 360 Creator Bank backend is built entirely on Supabase's Backend-as-a-Service (BaaS) platform, providing a comprehensive API for creator banking and investment management.

## Base URLs

- **Local Development**: `http://127.0.0.1:54321`
- **Production**: `https://your-project.supabase.co`

## Authentication

All API endpoints require authentication using Supabase Auth JWT tokens.

### Headers Required
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Registration API Endpoints

The registration process consists of 5 sequential steps that must be completed in order.

### Step 1: Referral Information

**Endpoint**: `POST /functions/v1/register-step-1`

**Description**: Submit referral information and invite code validation.

**Request Body**:
```json
{
  "invite_code": "12345",
  "referrer_full_name": "<PERSON>",
  "relationship_to_referrer": "Colleague",
  "how_met_referrer": "Through work conference"
}
```

**Response (Success)**:
```json
{
  "status": "success",
  "message": "Referral information saved successfully.",
  "next_step": "personal_information"
}
```

**Response (Error)**:
```json
{
  "status": "error",
  "code": "INVALID_INVITE_CODE",
  "message": "The provided invite code is invalid or expired."
}
```

### Step 2: Personal Information

**Endpoint**: `POST /functions/v1/register-step-2`

**Description**: Submit personal details for KYC verification.

**Request Body**:
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "date_of_birth": "1990-01-15",
  "phone_number": "+15551234567",
  "address": "123 Main St",
  "city": "Anytown",
  "country": "USA",
  "postal_code": "12345"
}
```

**Response (Success)**:
```json
{
  "status": "success",
  "message": "Personal information saved successfully.",
  "next_step": "creator_profile"
}
```

### Step 3: Creator Profile

**Endpoint**: `POST /functions/v1/register-step-3`

**Description**: Submit creator-specific profile information.

**Request Body**:
```json
{
  "primary_platform": "YouTube",
  "platform_username_handle": "@johndoe",
  "follower_subscriber_count": "50K - 100K",
  "monthly_revenue": "$10K - $25K",
  "content_category": "Education",
  "years_active_as_creator": "2-3 years"
}
```

**Valid Options**:
- **follower_subscriber_count**: "< 1K", "1K - 10K", "10K - 50K", "50K - 100K", "100K - 500K", "500K - 1M", "1M - 5M", "5M+"
- **monthly_revenue**: "< $1K", "$1K - $5K", "$5K - $10K", "$10K - $25K", "$25K - $50K", "$50K - $100K", "$100K+"
- **content_category**: "Education", "Entertainment", "Gaming", "Lifestyle", "Technology", "Business", "Health & Fitness", "Travel", "Food & Cooking", "Fashion & Beauty", "Music", "Art & Design", "Sports", "News & Politics", "Comedy", "Other"
- **years_active_as_creator**: "Less than 1 year", "1-2 years", "2-3 years", "3-5 years", "5-10 years", "10+ years"

**Response (Success)**:
```json
{
  "status": "success",
  "message": "Creator profile saved successfully.",
  "next_step": "document_verification"
}
```

### Step 4: Document Verification

**Endpoint**: `POST /functions/v1/register-step-4`

**Description**: Submit URLs of uploaded KYC documents.

**Request Body**:
```json
{
  "government_id_url": "https://your-project.supabase.co/storage/v1/object/public/kyc-documents/user-id/government_id.jpg",
  "proof_of_income_url": "https://your-project.supabase.co/storage/v1/object/public/kyc-documents/user-id/income.pdf",
  "platform_verification_url": "https://your-project.supabase.co/storage/v1/object/public/kyc-documents/user-id/platform.jpg",
  "business_documents_url": "https://your-project.supabase.co/storage/v1/object/public/kyc-documents/user-id/business.pdf"
}
```

**Note**: Documents must be uploaded using the `/functions/v1/upload-document` endpoint first.

**Response (Success)**:
```json
{
  "status": "success",
  "message": "Documents uploaded successfully. Awaiting review.",
  "next_step": "social_verification"
}
```

### Step 5: Social Verification

**Endpoint**: `POST /functions/v1/register-step-5`

**Description**: Submit social media profiles and portfolio links.

**Request Body**:
```json
{
  "linkedin_profile": "https://linkedin.com/in/johndoe",
  "personal_website": "https://johndoe.com",
  "portfolio_links": [
    "https://portfolio.com/project1",
    "https://portfolio.com/project2"
  ]
}
```

**Response (Success)**:
```json
{
  "status": "success",
  "message": "Social verification links saved successfully.",
  "next_step": "registration_complete"
}
```

### Registration Complete

**Endpoint**: `POST /functions/v1/register-complete`

**Description**: Finalize the registration process.

**Request Body**:
```json
{}
```

**Response (Success)**:
```json
{
  "status": "success",
  "message": "Registration complete. Welcome to 360 Creator Bank App!",
  "details": {
    "user_id": "uuid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "registration_completed_at": "2024-12-06T10:00:00Z",
    "next_steps": [
      "Document review (2-3 business days)",
      "Account approval notification",
      "Platform access activation"
    ]
  }
}
```

## File Upload Endpoint

### Upload Document

**Endpoint**: `POST /functions/v1/upload-document`

**Description**: Upload KYC documents securely to Supabase Storage.

**Content-Type**: `multipart/form-data`

**Form Fields**:
- `file`: The document file (max 10MB)
- `document_type`: One of: `government_id`, `proof_of_income`, `platform_verification`, `business_documents`

**Allowed File Types**:
- Images: JPEG, PNG, WebP
- Documents: PDF, DOC, DOCX

**Response (Success)**:
```json
{
  "status": "success",
  "message": "Document uploaded successfully",
  "data": {
    "document_url": "https://your-project.supabase.co/storage/v1/object/public/kyc-documents/user-id/document.jpg",
    "document_type": "government_id",
    "file_name": "government_id.jpg",
    "file_size": 1024000,
    "mime_type": "image/jpeg"
  }
}
```

## Auto-Generated REST API

Supabase automatically generates REST API endpoints for all database tables:

### Base URL
`https://your-project.supabase.co/rest/v1/`

### Common Endpoints

#### Users
- `GET /users` - List users (with RLS filtering)
- `GET /users?id=eq.{user_id}` - Get specific user
- `PATCH /users?id=eq.{user_id}` - Update user

#### Creators
- `GET /creators` - List creators
- `GET /creators?user_id=eq.{user_id}` - Get specific creator
- `POST /creators` - Create creator profile
- `PATCH /creators?user_id=eq.{user_id}` - Update creator profile

#### Deals
- `GET /deals` - List all deals
- `GET /deals?status=eq.open` - List open deals
- `POST /deals` - Create new deal
- `PATCH /deals?id=eq.{deal_id}` - Update deal

#### Investments
- `GET /investments` - List investments
- `POST /investments` - Create investment
- `GET /investments?investor_id=eq.{user_id}` - Get user's investments

#### Notifications
- `GET /notifications` - List user's notifications
- `PATCH /notifications?id=eq.{notification_id}` - Mark as read

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Input validation failed |
| `AUTHENTICATION_FAILED` | Invalid credentials |
| `UNAUTHORIZED_ACCESS` | Insufficient permissions |
| `RESOURCE_NOT_FOUND` | Requested resource not found |
| `DUPLICATE_ENTRY` | Resource already exists |
| `EXTERNAL_SERVICE_ERROR` | Third-party service error |
| `INTERNAL_SERVER_ERROR` | Unexpected server error |
| `METHOD_NOT_ALLOWED` | HTTP method not supported |
| `INVALID_TOKEN` | JWT token invalid or expired |
| `DATABASE_ERROR` | Database operation failed |
| `UPLOAD_FAILED` | File upload failed |
| `FILE_TOO_LARGE` | File exceeds size limit |
| `INVALID_FILE_TYPE` | Unsupported file type |

## Rate Limits

- Registration endpoints: 5 requests per minute per IP
- File upload: 10 requests per minute per user
- General API: 100 requests per minute per user

## Security

- All endpoints require valid JWT authentication
- Row Level Security (RLS) enforced on all database operations
- File uploads validated for type and size
- Input sanitization and validation on all endpoints
- CORS configured for secure cross-origin requests
