-- Initial schema for 360 Creator Bank App
-- This migration creates all the core tables as defined in the requirements

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types for better data integrity
CREATE TYPE user_role AS ENUM ('creator', 'investor', 'admin', 'manager', 'finance_officer');
CREATE TYPE kyc_status AS ENUM ('pending', 'approved', 'rejected', 'under_review');
CREATE TYPE deal_type AS ENUM ('debt', 'equity', 'staking');
CREATE TYPE deal_status AS ENUM ('open', 'closed', 'funded', 'cancelled');
CREATE TYPE transaction_type AS ENUM ('deposit', 'withdrawal', 'investment', 'payout', 'fee', 'refund');
CREATE TYPE transaction_status AS ENUM ('pending', 'completed', 'failed', 'cancelled');
CREATE TYPE notification_type AS ENUM ('email', 'in_app', 'sms');
CREATE TYPE referral_status AS ENUM ('pending', 'completed', 'rewarded', 'expired');
CREATE TYPE reward_type AS ENUM ('cash', 'access_upgrade', 'credit_bonus');
CREATE TYPE reward_status AS ENUM ('pending', 'awarded', 'redeemed', 'expired');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    role user_role DEFAULT 'creator' NOT NULL,
    kyc_status kyc_status DEFAULT 'pending' NOT NULL,
    first_name TEXT,
    last_name TEXT,
    phone_number TEXT,
    date_of_birth DATE,
    address JSONB,
    profile_picture TEXT,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    last_login_at TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}' NOT NULL
);

-- Creators table
CREATE TABLE public.creators (
    user_id UUID PRIMARY KEY REFERENCES public.users(id) ON DELETE CASCADE,
    creator_type TEXT,
    primary_platform TEXT,
    platform_username_handle TEXT,
    follower_subscriber_count TEXT,
    monthly_revenue TEXT,
    content_category TEXT,
    years_active_as_creator TEXT,
    portfolio_url TEXT,
    bio TEXT,
    social_links JSONB DEFAULT '{}' NOT NULL,
    verification_status TEXT DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Investors table
CREATE TABLE public.investors (
    user_id UUID PRIMARY KEY REFERENCES public.users(id) ON DELETE CASCADE,
    investor_type TEXT,
    investment_focus TEXT,
    accreditation_status TEXT,
    risk_tolerance TEXT,
    investment_experience TEXT,
    annual_income_range TEXT,
    net_worth_range TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Deals table
CREATE TABLE public.deals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    creator_id UUID NOT NULL REFERENCES public.creators(user_id) ON DELETE CASCADE,
    deal_type deal_type NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    amount_sought NUMERIC(15,2) NOT NULL CHECK (amount_sought > 0),
    amount_raised NUMERIC(15,2) DEFAULT 0 CHECK (amount_raised >= 0),
    currency TEXT DEFAULT 'USD' NOT NULL,
    status deal_status DEFAULT 'open' NOT NULL,
    start_date TIMESTAMPTZ,
    end_date TIMESTAMPTZ,
    terms JSONB DEFAULT '{}' NOT NULL,
    roi_projection NUMERIC(5,2),
    minimum_investment NUMERIC(15,2),
    maximum_investment NUMERIC(15,2),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    CONSTRAINT valid_date_range CHECK (end_date IS NULL OR start_date IS NULL OR end_date > start_date),
    CONSTRAINT valid_amount_raised CHECK (amount_raised <= amount_sought)
);

-- Investments table
CREATE TABLE public.investments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    investor_id UUID NOT NULL REFERENCES public.investors(user_id) ON DELETE CASCADE,
    deal_id UUID NOT NULL REFERENCES public.deals(id) ON DELETE CASCADE,
    amount NUMERIC(15,2) NOT NULL CHECK (amount > 0),
    investment_date TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    status transaction_status DEFAULT 'pending' NOT NULL,
    expected_return NUMERIC(15,2),
    actual_return NUMERIC(15,2),
    return_date TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    UNIQUE(investor_id, deal_id) -- Prevent duplicate investments in same deal
);

-- Transactions table
CREATE TABLE public.transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    type transaction_type NOT NULL,
    amount NUMERIC(15,2) NOT NULL CHECK (amount > 0),
    currency TEXT DEFAULT 'USD' NOT NULL,
    status transaction_status DEFAULT 'pending' NOT NULL,
    transaction_date TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    external_id TEXT,
    reference_id UUID, -- Can reference deals, investments, etc.
    description TEXT,
    metadata JSONB DEFAULT '{}' NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Notifications table
CREATE TABLE public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    type notification_type NOT NULL,
    subject TEXT NOT NULL,
    body TEXT NOT NULL,
    sent_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    read_at TIMESTAMPTZ,
    is_read BOOLEAN DEFAULT FALSE NOT NULL,
    metadata JSONB DEFAULT '{}' NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Referrals table
CREATE TABLE public.referrals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    referrer_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    referred_user_id UUID UNIQUE REFERENCES public.users(id) ON DELETE CASCADE,
    referral_code TEXT NOT NULL,
    invite_code TEXT,
    referrer_full_name TEXT,
    relationship_to_referrer TEXT,
    how_met_referrer TEXT,
    status referral_status DEFAULT 'pending' NOT NULL,
    referred_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Referral rewards table
CREATE TABLE public.referral_rewards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    referral_id UUID NOT NULL REFERENCES public.referrals(id) ON DELETE CASCADE,
    reward_type reward_type NOT NULL,
    amount NUMERIC(15,2),
    description TEXT,
    awarded_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    status reward_status DEFAULT 'pending' NOT NULL,
    redeemed_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Access control permissions table
CREATE TABLE public.access_control_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role user_role NOT NULL,
    resource TEXT NOT NULL,
    action TEXT NOT NULL,
    allowed BOOLEAN NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    UNIQUE(role, resource, action)
);

-- KYC documents table
CREATE TABLE public.kyc_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    document_type TEXT NOT NULL,
    document_url TEXT NOT NULL,
    file_name TEXT,
    file_size INTEGER,
    mime_type TEXT,
    uploaded_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    verified_at TIMESTAMPTZ,
    verified_by UUID REFERENCES public.users(id),
    status kyc_status DEFAULT 'pending' NOT NULL,
    rejection_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_role ON public.users(role);
CREATE INDEX idx_users_kyc_status ON public.users(kyc_status);
CREATE INDEX idx_deals_creator_id ON public.deals(creator_id);
CREATE INDEX idx_deals_status ON public.deals(status);
CREATE INDEX idx_deals_deal_type ON public.deals(deal_type);
CREATE INDEX idx_investments_investor_id ON public.investments(investor_id);
CREATE INDEX idx_investments_deal_id ON public.investments(deal_id);
CREATE INDEX idx_transactions_user_id ON public.transactions(user_id);
CREATE INDEX idx_transactions_type ON public.transactions(type);
CREATE INDEX idx_transactions_status ON public.transactions(status);
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX idx_referrals_referrer_id ON public.referrals(referrer_id);
CREATE INDEX idx_referrals_status ON public.referrals(status);
CREATE INDEX idx_kyc_documents_user_id ON public.kyc_documents(user_id);
CREATE INDEX idx_kyc_documents_status ON public.kyc_documents(status);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER handle_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_creators_updated_at
    BEFORE UPDATE ON public.creators
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_investors_updated_at
    BEFORE UPDATE ON public.investors
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_deals_updated_at
    BEFORE UPDATE ON public.deals
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_investments_updated_at
    BEFORE UPDATE ON public.investments
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_transactions_updated_at
    BEFORE UPDATE ON public.transactions
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_referrals_updated_at
    BEFORE UPDATE ON public.referrals
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_referral_rewards_updated_at
    BEFORE UPDATE ON public.referral_rewards
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_kyc_documents_updated_at
    BEFORE UPDATE ON public.kyc_documents
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
