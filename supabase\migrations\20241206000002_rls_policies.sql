-- Row Level Security (RLS) Policies for 360 Creator Bank App
-- This migration sets up comprehensive RLS policies for data security and access control

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.creators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.investors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.investments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referral_rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.access_control_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kyc_documents ENABLE ROW LEVEL SECURITY;

-- Helper function to check if user has specific role
CREATE OR REPLACE FUNCTION public.has_role(required_role user_role)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        SELECT role FROM public.users 
        WHERE id = auth.uid()
    ) = required_role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is admin or manager
CREATE OR REPLACE FUNCTION public.is_admin_or_manager()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        SELECT role FROM public.users 
        WHERE id = auth.uid()
    ) IN ('admin', 'manager');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has permission for specific action
CREATE OR REPLACE FUNCTION public.has_permission(resource_name TEXT, action_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_role_val user_role;
BEGIN
    SELECT role INTO user_role_val FROM public.users WHERE id = auth.uid();
    
    RETURN EXISTS (
        SELECT 1 FROM public.access_control_permissions 
        WHERE role = user_role_val 
        AND resource = resource_name 
        AND action = action_name 
        AND allowed = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON public.users
    FOR SELECT USING (has_role('admin'));

CREATE POLICY "Admins can update all users" ON public.users
    FOR UPDATE USING (has_role('admin'));

CREATE POLICY "Admins can insert users" ON public.users
    FOR INSERT WITH CHECK (has_role('admin'));

-- Creators table policies
CREATE POLICY "Creators can view their own profile" ON public.creators
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Creators can update their own profile" ON public.creators
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Creators can insert their own profile" ON public.creators
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Investors can view creator profiles" ON public.creators
    FOR SELECT USING (has_role('investor'));

CREATE POLICY "Admins can manage all creator profiles" ON public.creators
    FOR ALL USING (has_role('admin'));

-- Investors table policies
CREATE POLICY "Investors can view their own profile" ON public.investors
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Investors can update their own profile" ON public.investors
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Investors can insert their own profile" ON public.investors
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can manage all investor profiles" ON public.investors
    FOR ALL USING (has_role('admin'));

-- Deals table policies
CREATE POLICY "Anyone can view open deals" ON public.deals
    FOR SELECT USING (status = 'open' OR auth.uid() IS NOT NULL);

CREATE POLICY "Creators can manage their own deals" ON public.deals
    FOR ALL USING (
        creator_id = auth.uid() OR 
        creator_id IN (SELECT user_id FROM public.creators WHERE user_id = auth.uid())
    );

CREATE POLICY "Admins can manage all deals" ON public.deals
    FOR ALL USING (has_role('admin'));

-- Investments table policies
CREATE POLICY "Investors can view their own investments" ON public.investments
    FOR SELECT USING (
        investor_id = auth.uid() OR 
        investor_id IN (SELECT user_id FROM public.investors WHERE user_id = auth.uid())
    );

CREATE POLICY "Investors can create investments" ON public.investments
    FOR INSERT WITH CHECK (
        investor_id = auth.uid() OR 
        investor_id IN (SELECT user_id FROM public.investors WHERE user_id = auth.uid())
    );

CREATE POLICY "Creators can view investments in their deals" ON public.investments
    FOR SELECT USING (
        deal_id IN (
            SELECT id FROM public.deals 
            WHERE creator_id = auth.uid() OR 
            creator_id IN (SELECT user_id FROM public.creators WHERE user_id = auth.uid())
        )
    );

CREATE POLICY "Admins can manage all investments" ON public.investments
    FOR ALL USING (has_role('admin'));

-- Transactions table policies
CREATE POLICY "Users can view their own transactions" ON public.transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own transactions" ON public.transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Finance officers can view all transactions" ON public.transactions
    FOR SELECT USING (has_role('finance_officer') OR has_role('admin'));

CREATE POLICY "Admins can manage all transactions" ON public.transactions
    FOR ALL USING (has_role('admin'));

-- Notifications table policies
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications" ON public.notifications
    FOR INSERT WITH CHECK (true); -- Allow system to create notifications

CREATE POLICY "Admins can manage all notifications" ON public.notifications
    FOR ALL USING (has_role('admin'));

-- Referrals table policies
CREATE POLICY "Users can view referrals they made" ON public.referrals
    FOR SELECT USING (auth.uid() = referrer_id);

CREATE POLICY "Users can view referrals they received" ON public.referrals
    FOR SELECT USING (auth.uid() = referred_user_id);

CREATE POLICY "Users can create referrals" ON public.referrals
    FOR INSERT WITH CHECK (auth.uid() = referrer_id);

CREATE POLICY "Admins can manage all referrals" ON public.referrals
    FOR ALL USING (has_role('admin'));

-- Referral rewards table policies
CREATE POLICY "Users can view their referral rewards" ON public.referral_rewards
    FOR SELECT USING (
        referral_id IN (
            SELECT id FROM public.referrals 
            WHERE referrer_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all referral rewards" ON public.referral_rewards
    FOR ALL USING (has_role('admin'));

-- Access control permissions table policies
CREATE POLICY "Admins can manage permissions" ON public.access_control_permissions
    FOR ALL USING (has_role('admin'));

CREATE POLICY "Users can view permissions for their role" ON public.access_control_permissions
    FOR SELECT USING (
        role = (SELECT role FROM public.users WHERE id = auth.uid())
    );

-- KYC documents table policies
CREATE POLICY "Users can view their own KYC documents" ON public.kyc_documents
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can upload their own KYC documents" ON public.kyc_documents
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own KYC documents" ON public.kyc_documents
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all KYC documents" ON public.kyc_documents
    FOR ALL USING (has_role('admin'));

CREATE POLICY "Managers can view and verify KYC documents" ON public.kyc_documents
    FOR SELECT USING (has_role('manager') OR has_role('admin'));

CREATE POLICY "Managers can update KYC verification status" ON public.kyc_documents
    FOR UPDATE USING (has_role('manager') OR has_role('admin'));

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, role)
    VALUES (NEW.id, NEW.email, 'creator');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert default permissions for roles
INSERT INTO public.access_control_permissions (role, resource, action, allowed) VALUES
-- Admin permissions (full access)
('admin', 'users', 'read', true),
('admin', 'users', 'write', true),
('admin', 'users', 'delete', true),
('admin', 'deals', 'read', true),
('admin', 'deals', 'write', true),
('admin', 'deals', 'delete', true),
('admin', 'investments', 'read', true),
('admin', 'investments', 'write', true),
('admin', 'transactions', 'read', true),
('admin', 'transactions', 'write', true),
('admin', 'kyc', 'approve', true),
('admin', 'kyc', 'reject', true),

-- Manager permissions
('manager', 'users', 'read', true),
('manager', 'deals', 'read', true),
('manager', 'investments', 'read', true),
('manager', 'transactions', 'read', true),
('manager', 'kyc', 'approve', true),
('manager', 'kyc', 'reject', true),

-- Finance Officer permissions
('finance_officer', 'transactions', 'read', true),
('finance_officer', 'transactions', 'write', true),
('finance_officer', 'investments', 'read', true),

-- Creator permissions
('creator', 'deals', 'read', true),
('creator', 'deals', 'write', true),
('creator', 'investments', 'read', false),

-- Investor permissions
('investor', 'deals', 'read', true),
('investor', 'investments', 'read', true),
('investor', 'investments', 'write', true);
