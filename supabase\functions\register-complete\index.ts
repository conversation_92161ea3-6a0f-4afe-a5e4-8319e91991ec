// Registration Complete: Final step confirmation
// Handles the final confirmation of the multi-step registration process

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders, createCorsResponse, createCorsPreflightResponse } from '../_shared/cors.ts'

interface ApiResponse {
  status: 'success' | 'error'
  message: string
  code?: string
  details?: any
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return createCorsPreflightResponse()
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return createCorsResponse({
        status: 'error',
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only POST method is allowed'
      } as ApiResponse, 405)
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return createCorsResponse({
        status: 'error',
        code: 'UNAUTHORIZED',
        message: 'Authorization header is required'
      } as ApiResponse, 401)
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user from JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return createCorsResponse({
        status: 'error',
        code: 'INVALID_TOKEN',
        message: 'Invalid or expired token'
      } as ApiResponse, 401)
    }

    // Get current user data to verify registration completion
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('metadata, first_name, last_name, email')
      .eq('id', user.id)
      .single()

    if (userError) {
      console.error('Error fetching user data:', userError)
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to fetch user information'
      } as ApiResponse, 500)
    }

    // Check if registration is actually complete
    const requiredSteps = ['referral', 'personal_information', 'creator_profile', 'document_verification', 'social_verification']
    const completedSteps = userData.metadata?.registration_completed_steps || []
    const missingSteps = requiredSteps.filter(step => !completedSteps.includes(step))

    if (missingSteps.length > 0) {
      return createCorsResponse({
        status: 'error',
        code: 'INCOMPLETE_REGISTRATION',
        message: 'Registration is not complete. Please complete all required steps.',
        details: { 
          missing_steps: missingSteps,
          completed_steps: completedSteps
        }
      } as ApiResponse, 400)
    }

    // Mark registration as finalized
    const { error: finalizeError } = await supabase
      .from('users')
      .update({
        metadata: {
          ...userData.metadata || {},
          registration_finalized: true,
          registration_finalized_at: new Date().toISOString()
        }
      })
      .eq('id', user.id)

    if (finalizeError) {
      console.error('Error finalizing registration:', finalizeError)
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to finalize registration'
      } as ApiResponse, 500)
    }

    // Send final welcome email with next steps
    const userName = userData.first_name ? `${userData.first_name} ${userData.last_name || ''}`.trim() : 'Creator'
    
    const { error: notificationError } = await supabase
      .rpc('send_notification', {
        p_user_id: user.id,
        p_type: 'email',
        p_subject: 'Welcome to 360 Creator Bank - Next Steps',
        p_body: `Dear ${userName},

Welcome to 360 Creator Bank! Your registration has been successfully completed.

What happens next:
1. Our team will review your submitted documents within 2-3 business days
2. You will receive an email notification once your account is approved
3. Once approved, you can start creating deals and accessing all platform features

In the meantime, you can:
- Complete your profile with additional information
- Explore the platform and familiarize yourself with the features
- Refer friends and earn rewards

Thank you for joining 360 Creator Bank. We're excited to help you grow your creative business!

Best regards,
The 360 Creator Bank Team`,
        p_metadata: { 
          registration_complete: true,
          user_name: userName,
          email: userData.email
        }
      })

    if (notificationError) {
      console.error('Error sending final welcome notification:', notificationError)
      // Don't fail the request for notification errors
    }

    // Get user statistics for the response
    const { data: userStats, error: statsError } = await supabase
      .rpc('get_user_stats', { p_user_id: user.id })

    let stats = null
    if (!statsError && userStats) {
      stats = userStats
    }

    // Return success response with user information
    return createCorsResponse({
      status: 'success',
      message: 'Registration complete. Welcome to 360 Creator Bank App!',
      details: {
        user_id: user.id,
        email: userData.email,
        name: userName,
        registration_completed_at: userData.metadata?.registration_completed_at,
        next_steps: [
          'Document review (2-3 business days)',
          'Account approval notification',
          'Platform access activation'
        ],
        stats: stats
      }
    } as ApiResponse)

  } catch (error) {
    console.error('Unexpected error in register-complete:', error)
    return createCorsResponse({
      status: 'error',
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred'
    } as ApiResponse, 500)
  }
})
