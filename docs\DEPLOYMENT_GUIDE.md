# 360 Creator Bank Deployment Guide

This guide covers deploying the 360 Creator Bank backend using Supabase's managed infrastructure.

## Prerequisites

- Supabase account
- Supabase CLI installed locally
- Node.js 18+ (for CLI)
- Git repository access

## Local Development Setup

### 1. Install Supabase CLI

```bash
npm install -g supabase
```

### 2. <PERSON><PERSON> and Initialize

```bash
git clone <repository-url>
cd 360-creator-bank-backend
supabase init
```

### 3. Start Local Development

```bash
# Start all Supabase services locally
supabase start

# This will start:
# - PostgreSQL database
# - API server
# - Auth server
# - Storage server
# - Realtime server
# - Studio (web interface)
```

### 4. Apply Migrations

```bash
# Reset database and apply all migrations
supabase db reset

# Or apply migrations incrementally
supabase db push
```

### 5. Deploy Edge Functions Locally

```bash
# Deploy all functions
supabase functions deploy

# Deploy specific function
supabase functions deploy register-step-1

# Serve functions locally for development
supabase functions serve
```

## Production Deployment

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Create new project
3. Choose region closest to your users
4. Set strong database password
5. Wait for project initialization

### 2. Link Local Project

```bash
# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref <your-project-ref>
```

### 3. Deploy Database Schema

```bash
# Push all migrations to production
supabase db push

# Verify migrations were applied
supabase db diff
```

### 4. Deploy Edge Functions

```bash
# Deploy all functions to production
supabase functions deploy

# Deploy with environment variables
supabase secrets set OPENAI_API_KEY=your_key_here
supabase secrets set SENDGRID_API_KEY=your_key_here
```

### 5. Configure Storage

Storage buckets and policies are created automatically via migrations. Verify in Supabase Studio:

1. Go to Storage section
2. Confirm buckets exist:
   - `kyc-documents` (private)
   - `profile-pictures` (public)
   - `creator-content` (public)
3. Verify policies are applied correctly

### 6. Configure Authentication

1. Go to Authentication > Settings in Supabase Studio
2. Configure email templates
3. Set up email provider (SendGrid recommended)
4. Configure redirect URLs for your frontend
5. Enable/disable signup as needed

## Environment Configuration

### Required Environment Variables

Set these in Supabase Dashboard > Settings > API:

```bash
# Email service (SendGrid recommended)
SENDGRID_API_KEY=your_sendgrid_api_key

# AI services (optional)
OPENAI_API_KEY=your_openai_api_key

# External integrations (future)
STRIPE_SECRET_KEY=your_stripe_secret_key
PLAID_CLIENT_ID=your_plaid_client_id
PLAID_SECRET=your_plaid_secret
```

### Supabase Configuration

Update `supabase/config.toml` for production:

```toml
[auth]
site_url = "https://your-frontend-domain.com"
additional_redirect_urls = ["https://your-frontend-domain.com/auth/callback"]
enable_signup = true
minimum_password_length = 8

[auth.email]
enable_signup = true
enable_confirmations = true

[auth.email.smtp]
enabled = true
host = "smtp.sendgrid.net"
port = 587
user = "apikey"
pass = "env(SENDGRID_API_KEY)"
admin_email = "<EMAIL>"
sender_name = "360 Creator Bank"
```

## Database Seeding

### Production Data

For production, only seed essential data:

```sql
-- Insert default admin user (do this manually after deployment)
-- Create admin through Supabase Auth, then update role:
UPDATE public.users SET role = 'admin' WHERE email = '<EMAIL>';

-- Seed access control permissions (already in migrations)
-- Verify permissions are correctly set
SELECT * FROM public.access_control_permissions;
```

## Monitoring and Maintenance

### 1. Set Up Monitoring

1. Enable database metrics in Supabase Dashboard
2. Set up log retention policies
3. Configure alerts for:
   - High error rates
   - Database performance issues
   - Storage usage limits
   - Function execution failures

### 2. Backup Strategy

Supabase provides automatic backups, but also:

```bash
# Create manual backup
supabase db dump --data-only > backup.sql

# Restore from backup (if needed)
psql -h db.your-project.supabase.co -U postgres -d postgres < backup.sql
```

### 3. Regular Maintenance

```bash
# Update Supabase CLI
npm update -g supabase

# Check for migration drift
supabase db diff

# Clean up orphaned files
SELECT public.cleanup_orphaned_files();
```

## Security Checklist

### Database Security
- [ ] RLS enabled on all tables
- [ ] Policies tested for all user roles
- [ ] Sensitive data encrypted
- [ ] Database password is strong
- [ ] Connection pooling configured

### API Security
- [ ] CORS configured correctly
- [ ] Rate limiting implemented
- [ ] Input validation on all endpoints
- [ ] Error messages don't leak sensitive info
- [ ] JWT tokens have appropriate expiry

### Storage Security
- [ ] Private buckets for sensitive documents
- [ ] File type validation implemented
- [ ] File size limits enforced
- [ ] Storage policies tested

### Function Security
- [ ] Environment variables secured
- [ ] No hardcoded secrets
- [ ] Error handling doesn't expose internals
- [ ] Proper authentication checks

## Performance Optimization

### Database Optimization
```sql
-- Add indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_role ON public.users(email, role);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_status_created ON public.deals(status, created_at);

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM public.deals WHERE status = 'open';
```

### Function Optimization
- Use connection pooling for database queries
- Implement caching where appropriate
- Minimize cold start times
- Use batch operations for bulk updates

## Troubleshooting

### Common Issues

1. **Migration Failures**
   ```bash
   # Check migration status
   supabase migration list
   
   # Repair migrations
   supabase migration repair <timestamp>
   ```

2. **Function Deployment Issues**
   ```bash
   # Check function logs
   supabase functions logs register-step-1
   
   # Test function locally
   supabase functions serve --debug
   ```

3. **RLS Policy Issues**
   ```sql
   -- Test policies as specific user
   SET ROLE authenticated;
   SET request.jwt.claims TO '{"sub": "user-uuid", "role": "creator"}';
   SELECT * FROM public.deals;
   ```

4. **Storage Issues**
   ```bash
   # Check storage usage
   supabase storage ls kyc-documents
   
   # Test upload permissions
   supabase storage cp test.jpg kyc-documents/test/test.jpg
   ```

## Rollback Procedures

### Database Rollback
```bash
# Rollback last migration
supabase migration down

# Rollback to specific migration
supabase db reset --to <timestamp>
```

### Function Rollback
```bash
# Deploy previous version
git checkout <previous-commit>
supabase functions deploy
```

This deployment guide ensures a secure, scalable, and maintainable production deployment of the 360 Creator Bank backend.
