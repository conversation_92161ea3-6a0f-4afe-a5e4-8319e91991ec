// Shared CORS configuration for Edge Functions
// This file provides consistent CORS headers across all Edge Functions

export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE',
  'Access-Control-Max-Age': '86400', // 24 hours
}

// Helper function to create CORS response
export function createCorsResponse(data: any, status: number = 200) {
  return new Response(
    JSON.stringify(data),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    }
  )
}

// Helper function for CORS preflight response
export function createCorsPreflightResponse() {
  return new Response('ok', { headers: corsHeaders })
}
