# Backend Requirements for 360 Creator Bank App

This document outlines the detailed backend requirements for the 360 Creator Bank App, with a focus on implementation using Supabase and leveraging AI for specific functionalities. The requirements are derived from the provided Product Requirements Document (PRD).

## 8. Tech Stack (MVP-Ready)

### Backend (Fully Baas)

- **Backend-as-a-Service (BaaS):** Supabase (preferred over AWS for MVP)
- **Cloud Provider (if not fully BaaS):** AWS (for specific services if needed)

### Authentication & User Management

- **Tool:** Firebase Auth or Supabase (Supabase Auth is preferred for consistency with BaaS choice)
- **Requirements:**
  - User registration with KYC
  - Fine-grained permissions for roles (Admin, Manager, Finance Officer)
  - Mandatory 2FA for withdrawals, KYC, integrations
  - Multi-signature support in high-volume cases (Phase 2)

### Payments & Cards

- **Tool:** Stripe Treasury & Issuing
- **Requirements:**
  - Integration with Stripe for payment processing and card issuance.

### Bank Aggregation

- **Tool:** Plaid / TrueLayer / SaltEdge
- **Requirements:**
  - Aggregation of bank accounts for users.

### Crypto Infrastructure

- **Tool:** Transak / Coinbase Commerce
- **Requirements:**
  - Handling of crypto transactions and infrastructure.

### Smart Contracts

- **Tool:** Solidity on BASE / NEAR / Solana
- **Requirements:**
  - Development and deployment of smart contracts on specified blockchains.

### Notifications

- **Tool:** SendGrid / Postmark
- **Requirements:**
  - Email notification system for various triggers (KYC status changes, integration connected/disconnected, transaction alerts, weekly/monthly summaries).
  - User-customizable notification settings.

### AI Forecasting

- **Tool:** OpenAI + LangChain
- **Requirements:**
  - Integration of AI models for forecasting (specific use cases to be defined).

## Additional Backend-Related Features/Requirements from PRD

### 1. Capital Partners Overview

- **Purpose:** KYC, debit cards, current account infra
- **Integration Style:** Plug-and-play API (MVP-ready)

### 3. Investor Portal Features

- Investor registration with KYC
- View and choose creator deals (debt, equity, staking)
- Allocation & commitment management
- ROI tracking, performance reports, tax docs
- Segmented access by creator verticals

### 4. Revenue-Sharing & Staking Logic

- Gross Yield: 100% (from hedge pool or DeFi)
- Platform Take: 20% fee
- Creator Return: 40%
- Admin Reserve: 40% (buffer/partner share)
- Staking Mechanics:
  - Lock periods: 3 / 6 / 12 months
  - Visible APY
  - Early withdrawal penalties or limits

### 5. Creator Teams & Access Control

- Roles: Admin, Manager, Finance Officer
- Fine-grained permissions
- Mandatory 2FA for withdrawals, KYC, integrations
- Multi-signature support in high-volume cases (Phase 2)

### 6. Email Notifications System

- Via SendGrid, Postmark, or Mailgun
- Triggers:
  - Account creation & welcome
  - KYC status changes
  - Integration connected/disconnected
  - Transaction alerts (withdrawals, staking activity)
  - Weekly/monthly summaries
- User-customizable notification settings

### 7. Referral System

- Referral cap: 20-50 per creator
- Rewards:
  - Cash for each successful signup
  - Access upgrades (credit, card tiers, pools)
- Dashboard view: invitations sent, conversions, earnings

### 9. Tax Outsourcing

- Leverage TaxBit, Koinly, or Deel for tax handling
- Expose income reports monthly/quarterly/yearly
- Future: offer accountant marketplace (optional add-on)

### 10. White-Label MVP Priorities

- Use embedded, ready-made systems (Stripe, Plaid, etc.)
- Defer smart-contract complexity until traction
- Emphasize fast, data-rich dashboard and UX

## Supabase Specific Considerations

Given the choice of Supabase as the primary BaaS, the backend implementation should leverage the following Supabase features:

- **PostgreSQL Database:** Utilize Supabase's PostgreSQL database for all data storage, defining appropriate tables, relationships, and constraints based on the requirements above.
- **Supabase Auth:** Implement user authentication and authorization using Supabase Auth, integrating with existing identity providers if necessary, and managing user roles and permissions.
- **Realtime:** Explore the use of Supabase Realtime for features requiring real-time updates, such as transaction alerts or dashboard updates.
- **Storage:** Utilize Supabase Storage for storing user-generated content, documents (e.g., tax reports), or other files.
- **Edge Functions:** Implement serverless functions (Supabase Edge Functions) for custom backend logic, API endpoints, and integrations with third-party services (Stripe, Plaid, SendGrid, etc.).
- **Row Level Security (RLS):** Implement robust RLS policies to ensure data security and fine-grained access control based on user roles and permissions.
- **Database Webhooks:** Use database webhooks to trigger external services or functions based on database events (e.g., new user registration, transaction completion).
- **API Generation:** Leverage Supabase's auto-generated APIs (REST and GraphQL) for efficient frontend-backend communication.

## AI Augmentation for Backend Development

To assist an AI in developing this backend, the following aspects should be considered:

- **Clear Data Models:** Define precise database schemas (tables, columns, data types, relationships) for all entities mentioned in the requirements (Users, Creators, Investors, Deals, Transactions, etc.).
- **API Endpoints:** Specify required API endpoints for each feature, including HTTP methods, request/response formats (JSON), and authentication/authorization requirements.
- **Business Logic:** Clearly articulate the business logic for each feature, including validation rules, data transformations, and interactions with external services.
- **Error Handling:** Define expected error scenarios and corresponding error responses.
- **Security Considerations:** Highlight security requirements such as data encryption, input validation, and protection against common vulnerabilities.
- **Scalability:** Mention any scalability considerations or performance targets.
- **Integration Details:** Provide detailed specifications for integrating with each third-party tool (Stripe, Plaid, SendGrid, etc.), including API keys, authentication methods, and specific API calls.
- **Testing Scenarios:** Outline key testing scenarios for each feature to ensure correctness and robustness.

This document serves as a comprehensive guide for an AI to understand and develop the backend for the 360 Creator Bank App using Supabase, with a focus on leveraging AI augmentation for efficient development.

## Detailed Data Models

To enable an AI to effectively develop the backend, precise data models are crucial. These models define the structure of the data that will be stored in Supabase's PostgreSQL database, including tables, columns, data types, and relationships. This section outlines the core entities and their proposed schema.

### 1. Users Table

This table will store information about all users of the platform, including creators, investors, and administrators. It will integrate with Supabase Auth for authentication purposes.

| Column Name       | Data Type    | Constraints                                | Description                                                          |
| ----------------- | ------------ | ------------------------------------------ | -------------------------------------------------------------------- |
| `id`              | `UUID`       | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the user (from Supabase Auth)                  |
| `email`           | `TEXT`       | `UNIQUE`, `NOT NULL`                       | User's email address                                                 |
| `created_at`      | `TIMESTAMPZ` | `DEFAULT now()`                            | Timestamp of user creation                                           |
| `role`            | `TEXT`       | `NOT NULL`, `DEFAULT 'creator'`            | User's role (e.g., 'creator', 'investor', 'admin')                   |
| `kyc_status`      | `TEXT`       | `DEFAULT 'pending'`                        | Status of KYC verification (e.g., 'pending', 'approved', 'rejected') |
| `first_name`      | `TEXT`       |                                            | User's first name                                                    |
| `last_name`       | `TEXT`       |                                            | User's last name                                                     |
| `phone_number`    | `TEXT`       |                                            | User's phone number                                                  |
| `address`         | `JSONB`      |                                            | User's address details (structured JSON)                             |
| `profile_picture` | `TEXT`       |                                            | URL to user's profile picture (Supabase Storage)                     |

### 2. Creators Table

This table will store additional details specific to creators, linked to the `users` table.

| Column Name     | Data Type | Constraints                             | Description                                          |
| --------------- | --------- | --------------------------------------- | ---------------------------------------------------- |
| `user_id`       | `UUID`    | `PRIMARY KEY`, `FOREIGN KEY (users.id)` | Foreign key to the `users` table                     |
| `creator_type`  | `TEXT`    |                                         | Type of creator (e.g., musician, artist, influencer) |
| `portfolio_url` | `TEXT`    |                                         | URL to creator's portfolio                           |
| `bio`           | `TEXT`    |                                         | Creator's biography                                  |
| `social_links`  | `JSONB`   |                                         | Social media links (structured JSON)                 |

### 3. Investors Table

This table will store additional details specific to investors, linked to the `users` table.

| Column Name            | Data Type | Constraints                             | Description                                        |
| ---------------------- | --------- | --------------------------------------- | -------------------------------------------------- |
| `user_id`              | `UUID`    | `PRIMARY KEY`, `FOREIGN KEY (users.id)` | Foreign key to the `users` table                   |
| `investor_type`        | `TEXT`    |                                         | Type of investor (e.g., individual, institutional) |
| `investment_focus`     | `TEXT`    |                                         | Areas of investment interest                       |
| `accreditation_status` | `TEXT`    |                                         | Investor accreditation status                      |

### 4. Deals Table

This table will store information about various deals available on the platform (debt, equity, staking).

| Column Name      | Data Type    | Constraints                                  | Description                                                                         |
| ---------------- | ------------ | -------------------------------------------- | ----------------------------------------------------------------------------------- |
| `id`             | `UUID`       | `PRIMARY KEY`, `DEFAULT gen_random_uuid()`   | Unique identifier for the deal                                                      |
| `creator_id`     | `UUID`       | `NOT NULL`, `FOREIGN KEY (creators.user_id)` | Foreign key to the `creators` table                                                 |
| `deal_type`      | `TEXT`       | `NOT NULL`                                   | Type of deal (e.g., 'debt', 'equity', 'staking')                                    |
| `title`          | `TEXT`       | `NOT NULL`                                   | Title of the deal                                                                   |
| `description`    | `TEXT`       |                                              | Detailed description of the deal                                                    |
| `amount_sought`  | `NUMERIC`    | `NOT NULL`                                   | Total amount of funding sought                                                      |
| `amount_raised`  | `NUMERIC`    | `DEFAULT 0`                                  | Current amount of funding raised                                                    |
| `currency`       | `TEXT`       | `NOT NULL`, `DEFAULT 'USD'`                  | Currency of the deal                                                                |
| `status`         | `TEXT`       | `NOT NULL`, `DEFAULT 'open'`                 | Current status of the deal (e.g., 'open', 'closed', 'funded')                       |
| `start_date`     | `TIMESTAMPZ` |                                              | Start date of the deal                                                              |
| `end_date`       | `TIMESTAMPZ` |                                              | End date of the deal                                                                |
| `terms`          | `JSONB`      |                                              | Specific terms of the deal (e.g., interest rate, equity percentage, staking period) |
| `roi_projection` | `NUMERIC`    |                                              | Projected Return on Investment                                                      |

### 5. Investments Table

This table will record individual investments made by investors into deals.

| Column Name       | Data Type    | Constraints                                   | Description                                                          |
| ----------------- | ------------ | --------------------------------------------- | -------------------------------------------------------------------- |
| `id`              | `UUID`       | `PRIMARY KEY`, `DEFAULT gen_random_uuid()`    | Unique identifier for the investment                                 |
| `investor_id`     | `UUID`       | `NOT NULL`, `FOREIGN KEY (investors.user_id)` | Foreign key to the `investors` table                                 |
| `deal_id`         | `UUID`       | `NOT NULL`, `FOREIGN KEY (deals.id)`          | Foreign key to the `deals` table                                     |
| `amount`          | `NUMERIC`    | `NOT NULL`                                    | Amount invested                                                      |
| `investment_date` | `TIMESTAMPZ` | `DEFAULT now()`                               | Date of the investment                                               |
| `status`          | `TEXT`       | `NOT NULL`, `DEFAULT 'pending'`               | Status of the investment (e.g., 'pending', 'completed', 'cancelled') |

### 6. Transactions Table

This table will log all financial transactions within the platform.

| Column Name        | Data Type    | Constraints                                | Description                                                                 |
| ------------------ | ------------ | ------------------------------------------ | --------------------------------------------------------------------------- |
| `id`               | `UUID`       | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the transaction                                       |
| `user_id`          | `UUID`       | `NOT NULL`, `FOREIGN KEY (users.id)`       | User associated with the transaction                                        |
| `type`             | `TEXT`       | `NOT NULL`                                 | Type of transaction (e.g., 'deposit', 'withdrawal', 'investment', 'payout') |
| `amount`           | `NUMERIC`    | `NOT NULL`                                 | Amount of the transaction                                                   |
| `currency`         | `TEXT`       | `NOT NULL`, `DEFAULT 'USD'`                | Currency of the transaction                                                 |
| `status`           | `TEXT`       | `NOT NULL`, `DEFAULT 'pending'`            | Status of the transaction (e.g., 'pending', 'completed', 'failed')          |
| `transaction_date` | `TIMESTAMPZ` | `DEFAULT now()`                            | Date and time of the transaction                                            |
| `external_id`      | `TEXT`       |                                            | ID from external payment processor (e.g., Stripe)                           |

### 7. Notifications Table

This table will store all system notifications for users.

| Column Name | Data Type    | Constraints                                | Description                                       |
| ----------- | ------------ | ------------------------------------------ | ------------------------------------------------- |
| `id`        | `UUID`       | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the notification            |
| `user_id`   | `UUID`       | `NOT NULL`, `FOREIGN KEY (users.id)`       | Recipient of the notification                     |
| `type`      | `TEXT`       | `NOT NULL`                                 | Type of notification (e.g., 'email', 'in-app')    |
| `subject`   | `TEXT`       | `NOT NULL`                                 | Subject of the notification                       |
| `body`      | `TEXT`       | `NOT NULL`                                 | Content of the notification                       |
| `sent_at`   | `TIMESTAMPZ` | `DEFAULT now()`                            | Timestamp when the notification was sent          |
| `read_at`   | `TIMESTAMPZ` |                                            | Timestamp when the notification was read          |
| `is_read`   | `BOOLEAN`    | `DEFAULT FALSE`                            | Flag indicating if the notification has been read |

### 8. Referral System Tables

#### Referrals Table

| Column Name        | Data Type    | Constraints                                | Description                                                       |
| ------------------ | ------------ | ------------------------------------------ | ----------------------------------------------------------------- |
| `id`               | `UUID`       | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the referral                                |
| `referrer_id`      | `UUID`       | `NOT NULL`, `FOREIGN KEY (users.id)`       | User who made the referral                                        |
| `referred_user_id` | `UUID`       | `UNIQUE`, `FOREIGN KEY (users.id)`         | User who was referred                                             |
| `referral_code`    | `TEXT`       | `NOT NULL`                                 | Code used for the referral                                        |
| `status`           | `TEXT`       | `NOT NULL`, `DEFAULT 'pending'`            | Status of the referral (e.g., 'pending', 'completed', 'rewarded') |
| `referred_at`      | `TIMESTAMPZ` | `DEFAULT now()`                            | Timestamp of the referral                                         |

#### Referral Rewards Table

| Column Name   | Data Type    | Constraints                                | Description                                                   |
| ------------- | ------------ | ------------------------------------------ | ------------------------------------------------------------- |
| `id`          | `UUID`       | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the reward                              |
| `referral_id` | `UUID`       | `NOT NULL`, `FOREIGN KEY (referrals.id)`   | Foreign key to the `referrals` table                          |
| `reward_type` | `TEXT`       | `NOT NULL`                                 | Type of reward (e.g., 'cash', 'access_upgrade')               |
| `amount`      | `NUMERIC`    |                                            | Amount of cash reward                                         |
| `description` | `TEXT`       |                                            | Description of the reward                                     |
| `awarded_at`  | `TIMESTAMPZ` | `DEFAULT now()`                            | Timestamp when the reward was awarded                         |
| `status`      | `TEXT`       | `NOT NULL`, `DEFAULT 'pending'`            | Status of the reward (e.g., 'pending', 'awarded', 'redeemed') |

### 9. Access Control & Permissions Table

This table will manage fine-grained permissions for different user roles.

| Column Name | Data Type | Constraints                                | Description                                                      |
| ----------- | --------- | ------------------------------------------ | ---------------------------------------------------------------- |
| `id`        | `UUID`    | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the permission                             |
| `role`      | `TEXT`    | `NOT NULL`                                 | User role (e.g., 'admin', 'manager', 'finance_officer')          |
| `resource`  | `TEXT`    | `NOT NULL`                                 | Resource being accessed (e.g., 'deals', 'users', 'transactions') |
| `action`    | `TEXT`    | `NOT NULL`                                 | Action allowed (e.g., 'read', 'write', 'delete', 'approve')      |
| `allowed`   | `BOOLEAN` | `NOT NULL`                                 | Whether the action is allowed for the role                       |

These data models provide a foundational structure for the Supabase backend. The AI should use these as a basis for creating the necessary tables and relationships within the PostgreSQL database, ensuring proper indexing and constraints for optimal performance and data integrity.

## API Endpoints

This section defines the essential API endpoints required for the backend, detailing their purpose, HTTP methods, request/response formats, and authentication/authorization requirements. The AI should implement these endpoints using Supabase Edge Functions or by leveraging Supabase's auto-generated APIs where applicable.

### 1. User Management Endpoints

| Endpoint               | Method | Description                             | Request Body (JSON)                           | Response Body (JSON)                          | Authentication/Authorization    |
| ---------------------- | ------ | --------------------------------------- | --------------------------------------------- | --------------------------------------------- | ------------------------------- |
| `/auth/signup`         | `POST` | Register a new user                     | `{ "email": "", "password": "", "role": "" }` | `{ "user_id": "", "email": "" }`              | None                            |
| `/auth/login`          | `POST` | Authenticate user and get session token | `{ "email": "", "password": "" }`             | `{ "access_token": "", "refresh_token": "" }` | None                            |
| `/users/{user_id}`     | `GET`  | Retrieve user profile                   | None                                          | `{ "user": { ... } }`                         | Authenticated User              |
| `/users/{user_id}`     | `PUT`  | Update user profile                     | `{ "first_name": "", "last_name": "" }`       | `{ "user": { ... } }`                         | Authenticated User (Self/Admin) |
| `/users/{user_id}/kyc` | `POST` | Submit KYC documents                    | `{ "document_type": "", "document_url": "" }` | `{ "status": "pending" }`                     | Authenticated User              |
| `/users/{user_id}/kyc` | `PUT`  | Update KYC status (Admin only)          | `{ "status": "approved" }`                    | `{ "status": "approved" }`                    | Admin                           |

### 2. Deal Management Endpoints

| Endpoint           | Method | Description                                  | Request Body (JSON)                                       | Response Body (JSON)             | Authentication/Authorization        |
| ------------------ | ------ | -------------------------------------------- | --------------------------------------------------------- | -------------------------------- | ----------------------------------- |
| `/deals`           | `POST` | Create a new deal                            | `{ "title": "", "description": "", "amount_sought": "" }` | `{ "deal_id": "", "title": "" }` | Authenticated Creator               |
| `/deals`           | `GET`  | Retrieve all deals (with filters/pagination) | None                                                      | `[ { "deal": { ... } } ]`        | Authenticated User                  |
| `/deals/{deal_id}` | `GET`  | Retrieve a specific deal                     | None                                                      | `{ "deal": { ... } }`            | Authenticated User                  |
| `/deals/{deal_id}` | `PUT`  | Update a deal                                | `{ "status": "closed" }`                                  | `{ "deal": { ... } }`            | Authenticated Creator (Owner/Admin) |

### 3. Investment Endpoints

| Endpoint                       | Method | Description                    | Request Body (JSON)               | Response Body (JSON)                           | Authentication/Authorization     |
| ------------------------------ | ------ | ------------------------------ | --------------------------------- | ---------------------------------------------- | -------------------------------- |
| `/investments`                 | `POST` | Create a new investment        | `{ "deal_id": "", "amount": "" }` | `{ "investment_id": "", "status": "pending" }` | Authenticated Investor           |
| `/investments/{investment_id}` | `GET`  | Retrieve a specific investment | None                              | `{ "investment": { ... } }`                    | Authenticated User (Owner/Admin) |

### 4. Transaction Endpoints

| Endpoint                         | Method | Description                          | Request Body (JSON) | Response Body (JSON)             | Authentication/Authorization     |
| -------------------------------- | ------ | ------------------------------------ | ------------------- | -------------------------------- | -------------------------------- |
| `/transactions`                  | `GET`  | Retrieve all transactions for a user | None                | `[ { "transaction": { ... } } ]` | Authenticated User               |
| `/transactions/{transaction_id}` | `GET`  | Retrieve a specific transaction      | None                | `{ "transaction": { ... } }`     | Authenticated User (Owner/Admin) |

### 5. Notification Endpoints

| Endpoint                                | Method | Description                           | Request Body (JSON) | Response Body (JSON)              | Authentication/Authorization |
| --------------------------------------- | ------ | ------------------------------------- | ------------------- | --------------------------------- | ---------------------------- |
| `/notifications`                        | `GET`  | Retrieve all notifications for a user | None                | `[ { "notification": { ... } } ]` | Authenticated User           |
| `/notifications/{notification_id}/read` | `PUT`  | Mark notification as read             | None                | `{ "status": "read" }`            | Authenticated User (Owner)   |

### 6. Referral Endpoints

| Endpoint                          | Method | Description                        | Request Body (JSON)                   | Response Body (JSON)      | Authentication/Authorization |
| --------------------------------- | ------ | ---------------------------------- | ------------------------------------- | ------------------------- | ---------------------------- |
| `/referrals`                      | `POST` | Create a new referral              | `{ "referred_email": "" }`            | `{ "referral_code": "" }` | Authenticated User           |
| `/referrals/{referral_id}/reward` | `POST` | Award referral reward (Admin only) | `{ "reward_type": "", "amount": "" }` | `{ "status": "awarded" }` | Admin                        |

These API endpoints provide the necessary interface for the frontend to interact with the backend services. The AI should ensure proper validation of input data, secure handling of sensitive information, and efficient querying of the Supabase database for each endpoint.

## Business Logic

This section outlines the core business logic for key functionalities, detailing the rules, validations, and processes that the backend must enforce. The AI should translate these into efficient and robust code within Supabase Edge Functions or database triggers.

### 1. User Registration and Authentication

- **Email and Password Validation:**
  - Email must be a valid format and unique.
  - Password must meet complexity requirements (e.g., minimum length, special characters, numbers, uppercase/lowercase letters).
- **Role Assignment:**
  - Default role for new sign-ups is 'creator'. Admin roles are assigned manually by existing administrators.
- **KYC Workflow:**
  - Upon registration, user's KYC status is 'pending'.
  - Users must submit required KYC documents (e.g., ID, proof of address) via a dedicated API endpoint.
  - Submitted documents are stored securely in Supabase Storage.
  - An administrator reviews KYC documents and updates the `kyc_status` in the `users` table to 'approved' or 'rejected'.
  - Notifications are sent to the user regarding their KYC status change.

### 2. Deal Creation and Management

- **Creator-Only Access:**
  - Only authenticated users with the 'creator' role can create new deals.
- **Deal Data Validation:**
  - `title`, `description`, `amount_sought`, `deal_type`, and `currency` are mandatory fields.
  - `amount_sought` must be a positive numeric value.
  - `deal_type` must be one of 'debt', 'equity', or 'staking'.
  - `start_date` must be in the future or current date.
  - `end_date` must be after `start_date`.
- **Status Transitions:**
  - New deals are created with 'open' status.
  - Deals can transition to 'closed' or 'funded' based on investment activity or manual closure by the creator/admin.
- **Revenue Sharing & Staking Logic:**
  - **Gross Yield:** 100% of the returns from the hedge pool or DeFi protocol.
  - **Platform Take:** 20% fee on the gross yield.
  - **Creator Return:** 40% of the gross yield.
  - **Admin Reserve:** 40% of the gross yield (buffer/partner share).
  - **Staking Mechanics:** Enforce lock periods (3, 6, or 12 months) for staking deals. Calculate and display visible APY. Implement early withdrawal penalties or limits as defined in the `terms` JSONB field of the `deals` table.

### 3. Investment Processing

- **Investor-Only Access:**
  - Only authenticated users with the 'investor' role and 'approved' KYC status can make investments.
- **Investment Validation:**
  - `deal_id` must refer to an existing 'open' deal.
  - `amount` must be a positive numeric value and not exceed the remaining `amount_sought` for the deal.
- **Transaction Recording:**
  - Each investment creates a record in the `investments` table and a corresponding 'investment' type transaction in the `transactions` table.
- **Deal Funding Update:**
  - The `amount_raised` for the associated deal is updated upon successful investment.
  - If `amount_raised` reaches `amount_sought`, the deal status automatically transitions to 'funded'.

### 4. Financial Transactions

- **Withdrawal Logic:**
  - Requires mandatory 2FA verification.
  - Funds can only be withdrawn from available balances.
  - Integration with Stripe for actual fund transfers.
- **Deposit Logic:**
  - Integration with Stripe for processing deposits.
- **Bank Aggregation:**
  - Utilize Plaid/TrueLayer/SaltEdge APIs to securely connect and aggregate user bank accounts.
  - Store encrypted bank account tokens, not raw credentials.
- **Crypto Transactions:**
  - Integration with Transak/Coinbase Commerce for crypto deposits and withdrawals.
  - Ensure secure handling of crypto addresses and transaction details.

### 5. Notifications System

- **Trigger-Based Notifications:**
  - Automatically send email notifications for:
    - Account creation and welcome.
    - KYC status changes (approved, rejected).
    - Integration connected/disconnected status.
    - Transaction alerts (deposits, withdrawals, investments, staking activity).
    - Weekly/monthly summaries of financial activity.
- **User Customization:**
  - Allow users to opt-in/out of specific notification types via their profile settings.
- **Delivery Mechanism:**
  - Use SendGrid or Postmark for email delivery.
  - Store notification content and status in the `notifications` table.

### 6. Referral System

- **Referral Code Generation:**
  - Each user is assigned a unique referral code upon registration.
- **Referral Tracking:**
  - When a new user signs up using a referral code, a record is created in the `referrals` table, linking the referrer and the referred user.
- **Reward Logic:**
  - Rewards (cash or access upgrades) are awarded to the referrer once the referred user meets specific criteria (e.g., completes KYC, makes first investment).
  - Rewards are recorded in the `referral_rewards` table.
  - Admin approval may be required for certain reward types.

### 7. Access Control and Permissions

- **Role-Based Access Control (RBAC):**
  - Implement RBAC based on the `role` column in the `users` table and the `access_control_permissions` table.
  - Define granular permissions for 'admin', 'manager', and 'finance_officer' roles for various resources (deals, users, transactions, etc.) and actions (read, write, delete, approve).
- **Multi-Signature Support (Phase 2):**
  - For high-volume cases, implement multi-signature requirements for critical operations (e.g., large withdrawals), requiring approval from multiple authorized personnel.

### 8. AI Forecasting Integration

- **Data Ingestion:**
  - Backend should provide secure and efficient access to historical financial data (transactions, deals, investments) for the AI forecasting model (OpenAI + LangChain).
- **API for Forecasts:**
  - Expose an internal API endpoint for the AI model to push forecast results back into the database or a dedicated cache.
- **Triggering Forecasts:**
  - Define triggers (e.g., scheduled jobs, new deal creation) for initiating AI forecasting processes.

These business logic rules are critical for the correct and secure operation of the 360 Creator Bank App. The AI should prioritize implementing these rules with high accuracy and efficiency, leveraging Supabase's capabilities for data validation, triggers, and serverless functions.

## Error Handling

Effective error handling is crucial for a robust and user-friendly backend. This section outlines the general principles and specific considerations for error responses. The AI should implement consistent error structures across all API endpoints.

### 1. General Principles

- **Consistent Error Structure:** All error responses should follow a standardized JSON format, including a clear error code, a human-readable message, and optionally, specific details about the error.
- **Meaningful Error Messages:** Error messages should be informative enough for developers to understand the issue and for users to take corrective action, without exposing sensitive internal details.
- **Appropriate HTTP Status Codes:** Use standard HTTP status codes (e.g., 400 Bad Request, 401 Unauthorized, 403 Forbidden, 404 Not Found, 500 Internal Server Error) to indicate the nature of the error.
- **Logging:** All errors should be logged with sufficient detail (e.g., timestamp, request details, stack trace) for debugging and monitoring purposes.

### 2. Specific Error Scenarios and Responses

| Error Code               | HTTP Status                 | Description                  | Example Response (JSON)                                                                                                           |
| ------------------------ | --------------------------- | ---------------------------- | --------------------------------------------------------------------------------------------------------------------------------- |
| `VALIDATION_ERROR`       | `400 Bad Request`           | Input data failed validation | `{ "code": "VALIDATION_ERROR", "message": "Invalid email format.", "details": { "field": "email", "reason": "invalid_format" } }` |
| `AUTHENTICATION_FAILED`  | `401 Unauthorized`          | Invalid credentials          | `{ "code": "AUTHENTICATION_FAILED", "message": "Invalid email or password." }`                                                    |
| `UNAUTHORIZED_ACCESS`    | `403 Forbidden`             | User lacks permissions       | `{ "code": "UNAUTHORIZED_ACCESS", "message": "You do not have permission to perform this action." }`                              |
| `RESOURCE_NOT_FOUND`     | `404 Not Found`             | Requested resource not found | `{ "code": "RESOURCE_NOT_FOUND", "message": "Deal with ID 'xyz' not found." }`                                                    |
| `DUPLICATE_ENTRY`        | `409 Conflict`              | Resource already exists      | `{ "code": "DUPLICATE_ENTRY", "message": "User with this email already exists." }`                                                |
| `EXTERNAL_SERVICE_ERROR` | `502 Bad Gateway`           | Error from external service  | `{ "code": "EXTERNAL_SERVICE_ERROR", "message": "Failed to process payment with Stripe." }`                                       |
| `INTERNAL_SERVER_ERROR`  | `500 Internal Server Error` | Unexpected server error      | `{ "code": "INTERNAL_SERVER_ERROR", "message": "An unexpected error occurred." }`                                                 |

### 3. Supabase-Specific Error Handling

- **Database Errors:** Handle PostgreSQL errors gracefully, mapping them to appropriate API error responses.
- **Auth Errors:** Capture and translate Supabase Auth errors into user-friendly messages.
- **RLS Violations:** Explicitly handle cases where Row Level Security policies prevent access, returning `UNAUTHORIZED_ACCESS` or `FORBIDDEN` errors.

By adhering to these error handling guidelines, the backend will provide clear feedback to both frontend applications and end-users, improving the overall user experience and simplifying debugging.
