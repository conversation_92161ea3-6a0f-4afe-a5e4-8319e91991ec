// Shared validation utilities for Edge Functions
// This file provides common validation functions used across registration steps

export interface ValidationError {
  field: string
  reason: string
  message: string
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
}

// Email validation
export function validateEmail(email: string): ValidationResult {
  const errors: ValidationError[] = []
  
  if (!email) {
    errors.push({
      field: 'email',
      reason: 'required',
      message: 'Email is required'
    })
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.push({
      field: 'email',
      reason: 'invalid_format',
      message: 'Invalid email format'
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Phone number validation (international format)
export function validatePhoneNumber(phone: string): ValidationResult {
  const errors: ValidationError[] = []
  
  if (!phone) {
    errors.push({
      field: 'phone_number',
      reason: 'required',
      message: 'Phone number is required'
    })
  } else if (!/^\+[1-9]\d{1,14}$/.test(phone)) {
    errors.push({
      field: 'phone_number',
      reason: 'invalid_format',
      message: 'Phone number must be in international format (e.g., +1234567890)'
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Date of birth validation
export function validateDateOfBirth(dateStr: string): ValidationResult {
  const errors: ValidationError[] = []
  
  if (!dateStr) {
    errors.push({
      field: 'date_of_birth',
      reason: 'required',
      message: 'Date of birth is required'
    })
    return { isValid: false, errors }
  }
  
  const date = new Date(dateStr)
  const now = new Date()
  
  if (isNaN(date.getTime())) {
    errors.push({
      field: 'date_of_birth',
      reason: 'invalid_format',
      message: 'Invalid date format. Use YYYY-MM-DD'
    })
  } else {
    // Check if date is in the future
    if (date > now) {
      errors.push({
        field: 'date_of_birth',
        reason: 'future_date',
        message: 'Date of birth cannot be in the future'
      })
    }
    
    // Check minimum age (18 years)
    const eighteenYearsAgo = new Date()
    eighteenYearsAgo.setFullYear(now.getFullYear() - 18)
    
    if (date > eighteenYearsAgo) {
      errors.push({
        field: 'date_of_birth',
        reason: 'underage',
        message: 'Must be at least 18 years old'
      })
    }
    
    // Check maximum age (120 years)
    const oneHundredTwentyYearsAgo = new Date()
    oneHundredTwentyYearsAgo.setFullYear(now.getFullYear() - 120)
    
    if (date < oneHundredTwentyYearsAgo) {
      errors.push({
        field: 'date_of_birth',
        reason: 'invalid_age',
        message: 'Invalid date of birth'
      })
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Name validation
export function validateName(name: string, fieldName: string): ValidationResult {
  const errors: ValidationError[] = []
  
  if (!name) {
    errors.push({
      field: fieldName,
      reason: 'required',
      message: `${fieldName.replace('_', ' ')} is required`
    })
  } else if (name.length < 2) {
    errors.push({
      field: fieldName,
      reason: 'too_short',
      message: `${fieldName.replace('_', ' ')} must be at least 2 characters long`
    })
  } else if (name.length > 50) {
    errors.push({
      field: fieldName,
      reason: 'too_long',
      message: `${fieldName.replace('_', ' ')} must be less than 50 characters`
    })
  } else if (!/^[a-zA-Z\s'-]+$/.test(name)) {
    errors.push({
      field: fieldName,
      reason: 'invalid_characters',
      message: `${fieldName.replace('_', ' ')} can only contain letters, spaces, hyphens, and apostrophes`
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// URL validation
export function validateUrl(url: string, fieldName: string, required: boolean = false): ValidationResult {
  const errors: ValidationError[] = []
  
  if (!url) {
    if (required) {
      errors.push({
        field: fieldName,
        reason: 'required',
        message: `${fieldName.replace('_', ' ')} is required`
      })
    }
    return { isValid: !required, errors }
  }
  
  try {
    new URL(url)
    
    // Check if URL starts with http or https
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      errors.push({
        field: fieldName,
        reason: 'invalid_protocol',
        message: 'URL must start with http:// or https://'
      })
    }
  } catch {
    errors.push({
      field: fieldName,
      reason: 'invalid_format',
      message: 'Invalid URL format'
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Postal code validation (basic)
export function validatePostalCode(postalCode: string, country: string = 'USA'): ValidationResult {
  const errors: ValidationError[] = []
  
  if (!postalCode) {
    errors.push({
      field: 'postal_code',
      reason: 'required',
      message: 'Postal code is required'
    })
    return { isValid: false, errors }
  }
  
  // Basic validation patterns for different countries
  const patterns: { [key: string]: RegExp } = {
    'USA': /^\d{5}(-\d{4})?$/,
    'UK': /^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$/i,
    'CA': /^[A-Z]\d[A-Z]\s?\d[A-Z]\d$/i,
    'DE': /^\d{5}$/,
    'FR': /^\d{5}$/
  }
  
  const pattern = patterns[country] || patterns['USA'] // Default to USA format
  
  if (!pattern.test(postalCode)) {
    errors.push({
      field: 'postal_code',
      reason: 'invalid_format',
      message: `Invalid postal code format for ${country}`
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Generic required field validation
export function validateRequired(value: any, fieldName: string): ValidationResult {
  const errors: ValidationError[] = []
  
  if (value === null || value === undefined || value === '') {
    errors.push({
      field: fieldName,
      reason: 'required',
      message: `${fieldName.replace('_', ' ')} is required`
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Combine multiple validation results
export function combineValidationResults(...results: ValidationResult[]): ValidationResult {
  const allErrors = results.flatMap(result => result.errors)
  
  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  }
}

// Validate follower count format
export function validateFollowerCount(count: string): ValidationResult {
  const errors: ValidationError[] = []
  
  const validFormats = [
    '< 1K', '1K - 10K', '10K - 50K', '50K - 100K', 
    '100K - 500K', '500K - 1M', '1M - 5M', '5M+'
  ]
  
  if (!validFormats.includes(count)) {
    errors.push({
      field: 'follower_subscriber_count',
      reason: 'invalid_value',
      message: 'Invalid follower count format'
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Validate revenue range format
export function validateRevenueRange(revenue: string): ValidationResult {
  const errors: ValidationError[] = []
  
  const validFormats = [
    '< $1K', '$1K - $5K', '$5K - $10K', '$10K - $25K', 
    '$25K - $50K', '$50K - $100K', '$100K+'
  ]
  
  if (!validFormats.includes(revenue)) {
    errors.push({
      field: 'monthly_revenue',
      reason: 'invalid_value',
      message: 'Invalid revenue range format'
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}
