# Phase-Wise Backend Requirements for 360 Creator Bank App

This document outlines the backend development for the 360 Creator Bank App in a phase-wise manner, designed for step-by-step implementation by an AI. Each phase builds upon the previous one, ensuring a structured and manageable development process. The primary backend technology is Supabase, leveraging its features for database, authentication, and serverless functions.

## Overall Goal

To develop a robust and scalable backend for the 360 Creator Bank App, enabling secure user management, financial transactions, deal management, and advanced features, primarily using Supabase.

## Phase 1: Core Infrastructure Setup

**Goal:** Establish the foundational Supabase project, define core user-related data models, and implement basic authentication.

**Key Features:**
- Supabase Project Initialization
- User Authentication (Sign-up, Login)
- User Profile Management
- Basic User Roles (Creator, Investor, Admin)

**Data Models (Supabase PostgreSQL Tables):**

### 1. Users Table

This table will store information about all users of the platform, including creators, investors, and administrators. It will integrate with Supabase Auth for authentication purposes.

| Column Name      | Data Type       | Constraints                               | Description                                     |
|------------------|-----------------|-------------------------------------------|-------------------------------------------------|
| `id`             | `UUID`          | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the user (from Supabase Auth) |
| `email`          | `TEXT`          | `UNIQUE`, `NOT NULL`                      | User's email address                            |
| `created_at`     | `TIMESTAMPZ`    | `DEFAULT now()`                           | Timestamp of user creation                      |
| `role`           | `TEXT`          | `NOT NULL`, `DEFAULT 'creator'`           | User's role (e.g., 'creator', 'investor', 'admin') |
| `kyc_status`     | `TEXT`          | `DEFAULT 'pending'`                       | Status of KYC verification (e.g., 'pending', 'approved', 'rejected') |
| `first_name`     | `TEXT`          |                                           | User's first name                               |
| `last_name`      | `TEXT`          |                                           | User's last name                                |
| `phone_number`   | `TEXT`          |                                           | User's phone number                             |
| `address`        | `JSONB`         |                                           | User's address details (structured JSON)        |
| `profile_picture`| `TEXT`          |                                           | URL to user's profile picture (Supabase Storage) |

### 2. Creators Table

This table will store additional details specific to creators, linked to the `users` table.

| Column Name      | Data Type       | Constraints                               | Description                                     |
|------------------|-----------------|-------------------------------------------|-------------------------------------------------|
| `user_id`        | `UUID`          | `PRIMARY KEY`, `FOREIGN KEY (users.id)`   | Foreign key to the `users` table                |
| `creator_type`   | `TEXT`          |                                           | Type of creator (e.g., musician, artist, influencer) |
| `portfolio_url`  | `TEXT`          |                                           | URL to creator's portfolio                      |
| `bio`            | `TEXT`          |                                           | Creator's biography                             |
| `social_links`   | `JSONB`         |                                           | Social media links (structured JSON)            |

### 3. Investors Table

This table will store additional details specific to investors, linked to the `users` table.

| Column Name      | Data Type       | Constraints                               | Description                                     |
|------------------|-----------------|-------------------------------------------|-------------------------------------------------|
| `user_id`        | `UUID`          | `PRIMARY KEY`, `FOREIGN KEY (users.id)`   | Foreign key to the `users` table                |
| `investor_type`  | `TEXT`          |                                           | Type of investor (e.g., individual, institutional) |
| `investment_focus`| `TEXT`          |                                           | Areas of investment interest                    |
| `accreditation_status`| `TEXT`      |                                           | Investor accreditation status                   |

**API Endpoints:**

| Endpoint                 | Method | Description                                  | Request Body (JSON)                                  | Response Body (JSON)                                | Authentication/Authorization |
|--------------------------|--------|----------------------------------------------|------------------------------------------------------|-----------------------------------------------------|------------------------------|
| `/auth/signup`           | `POST` | Register a new user                          | `{ "email": "", "password": "", "role": "" }` | `{ "user_id": "", "email": "" }`                 | None                         |
| `/auth/login`            | `POST` | Authenticate user and get session token      | `{ "email": "", "password": "" }`             | `{ "access_token": "", "refresh_token": "" }` | None                         |
| `/users/{user_id}`       | `GET`  | Retrieve user profile                        | None                                                 | `{ "user": { ... } }`                             | Authenticated User           |
| `/users/{user_id}`       | `PUT`  | Update user profile                          | `{ "first_name": "", "last_name": "" }`         | `{ "user": { ... } }`                             | Authenticated User (Self/Admin) |
| `/users/{user_id}/kyc`   | `POST` | Submit KYC documents                         | `{ "document_type": "", "document_url": "" }` | `{ "status": "pending" }`                       | Authenticated User           |
| `/users/{user_id}/kyc`   | `PUT`  | Update KYC status (Admin only)               | `{ "status": "approved" }`                         | `{ "status": "approved" }`                      | Admin                        |

**Business Logic:**
- Email and password validation during signup.
- Default role assignment for new users.
- KYC status management and document storage.
- User profile updates.

**Supabase Specifics:**
- Utilize Supabase Auth for user authentication.
- Define Row Level Security (RLS) policies for `users`, `creators`, and `investors` tables to ensure data privacy and access control.
- Use Supabase Storage for KYC documents and profile pictures.

## Phase 2: Deal and Investment Management

**Goal:** Implement the core functionality for managing deals and investments on the platform.

**Key Features:**
- Deal Creation and Listing
- Investment Tracking
- Revenue Sharing and Staking Logic (basic)

**Data Models (Supabase PostgreSQL Tables):**

### 4. Deals Table

This table will store information about various deals available on the platform (debt, equity, staking).

| Column Name      | Data Type       | Constraints                               | Description                                     |
|------------------|-----------------|-------------------------------------------|-------------------------------------------------|
| `id`             | `UUID`          | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the deal                  |
| `creator_id`     | `UUID`          | `NOT NULL`, `FOREIGN KEY (creators.user_id)` | Foreign key to the `creators` table             |
| `deal_type`      | `TEXT`          | `NOT NULL`                                | Type of deal (e.g., 'debt', 'equity', 'staking') |
| `title`          | `TEXT`          | `NOT NULL`                                | Title of the deal                               |
| `description`    | `TEXT`          |                                           | Detailed description of the deal                |
| `amount_sought`  | `NUMERIC`       | `NOT NULL`                                | Total amount of funding sought                  |
| `amount_raised`  | `NUMERIC`       | `DEFAULT 0`                               | Current amount of funding raised                |
| `currency`       | `TEXT`          | `NOT NULL`, `DEFAULT 'USD'`               | Currency of the deal                            |
| `status`         | `TEXT`          | `NOT NULL`, `DEFAULT 'open'`              | Current status of the deal (e.g., 'open', 'closed', 'funded') |
| `start_date`     | `TIMESTAMPZ`    |                                           | Start date of the deal                          |
| `end_date`       | `TIMESTAMPZ`    |                                           | End date of the deal                            |
| `terms`          | `JSONB`         |                                           | Specific terms of the deal (e.g., interest rate, equity percentage, staking period) |
| `roi_projection` | `NUMERIC`       |                                           | Projected Return on Investment                  |

### 5. Investments Table

This table will record individual investments made by investors into deals.

| Column Name      | Data Type       | Constraints                               | Description                                     |
|------------------|-----------------|-------------------------------------------|-------------------------------------------------|
| `id`             | `UUID`          | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the investment            |
| `investor_id`    | `UUID`          | `NOT NULL`, `FOREIGN KEY (investors.user_id)` | Foreign key to the `investors` table            |
| `deal_id`        | `UUID`          | `NOT NULL`, `FOREIGN KEY (deals.id)`      | Foreign key to the `deals` table                |
| `amount`         | `NUMERIC`       | `NOT NULL`                                | Amount invested                                 |
| `investment_date`| `TIMESTAMPZ`    | `DEFAULT now()`                           | Date of the investment                          |
| `status`         | `TEXT`          | `NOT NULL`, `DEFAULT 'pending'`           | Status of the investment (e.g., 'pending', 'completed', 'cancelled') |

**API Endpoints:**

| Endpoint                 | Method | Description                                  | Request Body (JSON)                                  | Response Body (JSON)                                | Authentication/Authorization |
|--------------------------|--------|----------------------------------------------|------------------------------------------------------|-----------------------------------------------------|------------------------------|
| `/deals`                 | `POST` | Create a new deal                            | `{ "title": "", "description": "", "amount_sought": "" }` | `{ "deal_id": "", "title": "" }`                 | Authenticated Creator        |
| `/deals`                 | `GET`  | Retrieve all deals (with filters/pagination) | None                                                 | `[ { "deal": { ... } } ]`                         | Authenticated User           |
| `/deals/{deal_id}`       | `GET`  | Retrieve a specific deal                     | None                                                 | `{ "deal": { ... } }`                             | Authenticated User           |
| `/deals/{deal_id}`       | `PUT`  | Update a deal                                | `{ "status": "closed" }`                           | `{ "deal": { ... } }`                             | Authenticated Creator (Owner/Admin) |
| `/investments`           | `POST` | Create a new investment                      | `{ "deal_id": "", "amount": "" }`               | `{ "investment_id": "", "status": "pending" }` | Authenticated Investor       |
| `/investments/{investment_id}` | `GET` | Retrieve a specific investment               | None                                                 | `{ "investment": { ... } }`                       | Authenticated User (Owner/Admin) |

**Business Logic:**
- Only creators can create deals.
- Validation for deal creation (mandatory fields, dates).
- Investment validation (amount, deal status).
- Automatic update of `amount_raised` and deal status upon investment.
- Basic revenue sharing and staking logic (calculation of platform take, creator return, admin reserve).

**Supabase Specifics:**
- Implement RLS for `deals` and `investments` tables.
- Use database triggers or Supabase Edge Functions for automatic updates of `amount_raised` and deal status.

## Phase 3: Financial Transactions and Integrations

**Goal:** Implement the financial transaction system and integrate with external payment and banking services.

**Key Features:**
- Deposit and Withdrawal Functionality
- Bank Account Aggregation
- Crypto Transaction Handling

**Data Models (Supabase PostgreSQL Tables):**

### 6. Transactions Table

This table will log all financial transactions within the platform.

| Column Name      | Data Type       | Constraints                               | Description                                     |
|------------------|-----------------|-------------------------------------------|-------------------------------------------------|
| `id`             | `UUID`          | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the transaction           |
| `user_id`        | `UUID`          | `NOT NULL`, `FOREIGN KEY (users.id)`      | User associated with the transaction            |
| `type`           | `TEXT`          | `NOT NULL`                                | Type of transaction (e.g., 'deposit', 'withdrawal', 'investment', 'payout') |
| `amount`         | `NUMERIC`       | `NOT NULL`                                | Amount of the transaction                       |
| `currency`       | `TEXT`          | `NOT NULL`, `DEFAULT 'USD'`               | Currency of the transaction                     |
| `status`         | `TEXT`          | `NOT NULL`, `DEFAULT 'pending'`           | Status of the transaction (e.g., 'pending', 'completed', 'failed') |
| `transaction_date`| `TIMESTAMPZ`    | `DEFAULT now()`                           | Date and time of the transaction                |
| `external_id`    | `TEXT`          |                                           | ID from external payment processor (e.g., Stripe) |

**API Endpoints:**

| Endpoint                 | Method | Description                                  | Request Body (JSON)                                  | Response Body (JSON)                                | Authentication/Authorization |
|--------------------------|--------|----------------------------------------------|------------------------------------------------------|-----------------------------------------------------|------------------------------|
| `/transactions`          | `GET`  | Retrieve all transactions for a user         | None                                                 | `[ { "transaction": { ... } } ]`                  | Authenticated User           |
| `/transactions/{transaction_id}` | `GET` | Retrieve a specific transaction              | None                                                 | `{ "transaction": { ... } }`                      | Authenticated User (Owner/Admin) |
| `/payments/deposit`      | `POST` | Initiate a deposit                           | `{ "amount": "", "currency": "", "payment_method_id": "" }` | `{ "transaction_id": "", "status": "pending" }` | Authenticated User           |
| `/payments/withdrawal`   | `POST` | Initiate a withdrawal                        | `{ "amount": "", "currency": "", "bank_account_id": "" }` | `{ "transaction_id": "", "status": "pending" }` | Authenticated User (2FA required) |
| `/bank-accounts`         | `POST` | Connect a bank account (Plaid/TrueLayer)     | `{ "public_token": "", "metadata": { ... } }`     | `{ "bank_account_id": "", "status": "connected" }` | Authenticated User           |
| `/crypto/deposit`        | `POST` | Generate crypto deposit address              | `{ "currency": "" }`                               | `{ "address": "", "memo": "" }`                | Authenticated User           |
| `/crypto/withdrawal`     | `POST` | Initiate crypto withdrawal                   | `{ "amount": "", "currency": "", "address": "" }` | `{ "transaction_id": "", "status": "pending" }` | Authenticated User (2FA required) |

**Business Logic:**
- Integration with Stripe for deposits and withdrawals.
- Integration with Plaid/TrueLayer/SaltEdge for bank account aggregation.
- Integration with Transak/Coinbase Commerce for crypto transactions.
- Mandatory 2FA for withdrawals (both fiat and crypto).
- Secure storage of bank account tokens (not raw credentials).

**Supabase Specifics:**
- Use Supabase Edge Functions for handling sensitive payment and banking integrations.
- Implement RLS for the `transactions` table.

## Phase 4: Notifications and Referral System

**Goal:** Implement the communication and growth-oriented features of the platform.

**Key Features:**
- Email Notification System
- In-app Notifications
- Referral Program

**Data Models (Supabase PostgreSQL Tables):**

### 7. Notifications Table

This table will store all system notifications for users.

| Column Name      | Data Type       | Constraints                               | Description                                     |
|------------------|-----------------|-------------------------------------------|-------------------------------------------------|
| `id`             | `UUID`          | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the notification          |
| `user_id`        | `UUID`          | `NOT NULL`, `FOREIGN KEY (users.id)`      | Recipient of the notification                   |
| `type`           | `TEXT`          | `NOT NULL`                                | Type of notification (e.g., 'email', 'in-app') |
| `subject`        | `TEXT`          | `NOT NULL`                                | Subject of the notification                     |
| `body`           | `TEXT`          | `NOT NULL`                                | Content of the notification                     |
| `sent_at`        | `TIMESTAMPZ`    | `DEFAULT now()`                           | Timestamp when the notification was sent        |
| `read_at`        | `TIMESTAMPZ`    |                                           | Timestamp when the notification was read        |
| `is_read`        | `BOOLEAN`       | `DEFAULT FALSE`                           | Flag indicating if the notification has been read |

### 8. Referral System Tables

#### Referrals Table

| Column Name      | Data Type       | Constraints                               | Description                                     |
|------------------|-----------------|-------------------------------------------|-------------------------------------------------|
| `id`             | `UUID`          | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the referral              |
| `referrer_id`    | `UUID`          | `NOT NULL`, `FOREIGN KEY (users.id)`      | User who made the referral                      |
| `referred_user_id`| `UUID`         | `UNIQUE`, `FOREIGN KEY (users.id)`        | User who was referred                           |
| `referral_code`  | `TEXT`          | `NOT NULL`                                | Code used for the referral                      |
| `status`         | `TEXT`          | `NOT NULL`, `DEFAULT 'pending'`           | Status of the referral (e.g., 'pending', 'completed', 'rewarded') |
| `referred_at`    | `TIMESTAMPZ`    | `DEFAULT now()`                           | Timestamp of the referral                       |

#### Referral Rewards Table

| Column Name      | Data Type       | Constraints                               | Description                                     |
|------------------|-----------------|-------------------------------------------|-------------------------------------------------|
| `id`             | `UUID`          | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the reward                |
| `referral_id`    | `UUID`          | `NOT NULL`, `FOREIGN KEY (referrals.id)`  | Foreign key to the `referrals` table            |
| `reward_type`    | `TEXT`          | `NOT NULL`                                | Type of reward (e.g., 'cash', 'access_upgrade') |
| `amount`         | `NUMERIC`       |                                           | Amount of cash reward                           |
| `description`    | `TEXT`          |                                           | Description of the reward                       |
| `awarded_at`     | `TIMESTAMPZ`    | `DEFAULT now()`                           | Timestamp when the reward was awarded           |
| `status`         | `TEXT`          | `NOT NULL`, `DEFAULT 'pending'`           | Status of the reward (e.g., 'pending', 'awarded', 'redeemed') |

**API Endpoints:**

| Endpoint                 | Method | Description                                  | Request Body (JSON)                                  | Response Body (JSON)                                | Authentication/Authorization |
|--------------------------|--------|----------------------------------------------|------------------------------------------------------|-----------------------------------------------------|------------------------------|
| `/notifications`         | `GET`  | Retrieve all notifications for a user        | None                                                 | `[ { "notification": { ... } } ]`                 | Authenticated User           |
| `/notifications/{notification_id}/read` | `PUT` | Mark notification as read                    | None                                                 | `{ "status": "read" }`                          | Authenticated User (Owner)   |
| `/referrals`             | `POST` | Create a new referral                        | `{ "referred_email": "" }`                         | `{ "referral_code": "" }`                       | Authenticated User           |
| `/referrals/{referral_id}/reward` | `POST` | Award referral reward (Admin only)           | `{ "reward_type": "", "amount": "" }`           | `{ "status": "awarded" }`                       | Admin                        |

**Business Logic:**
- Trigger-based email notifications (account creation, KYC, transactions, summaries).
- User customization of notification preferences.
- Referral code generation and tracking.
- Reward logic for successful referrals.

**Supabase Specifics:**
- Use Supabase Edge Functions for sending emails via SendGrid/Postmark.
- Implement RLS for `notifications`, `referrals`, and `referral_rewards` tables.
- Utilize Database Webhooks to trigger notifications based on database events (e.g., new transaction, KYC status change).

## Phase 5: Advanced Features and AI Integration

**Goal:** Implement advanced functionalities, including smart contracts, AI forecasting, and comprehensive access control.

**Key Features:**
- Smart Contract Integration
- AI Forecasting Integration
- Fine-grained Access Control (Phase 2)

**Data Models (Supabase PostgreSQL Tables):**

### 9. Access Control & Permissions Table

This table will manage fine-grained permissions for different user roles.

| Column Name      | Data Type       | Constraints                               | Description                                     |
|------------------|-----------------|-------------------------------------------|-------------------------------------------------|
| `id`             | `UUID`          | `PRIMARY KEY`, `DEFAULT gen_random_uuid()` | Unique identifier for the permission            |
| `role`           | `TEXT`          | `NOT NULL`                                | User role (e.g., 'admin', 'manager', 'finance_officer') |
| `resource`       | `TEXT`          | `NOT NULL`                                | Resource being accessed (e.g., 'deals', 'users', 'transactions') |
| `action`         | `TEXT`          | `NOT NULL`                                | Action allowed (e.g., 'read', 'write', 'delete', 'approve') |
| `allowed`        | `BOOLEAN`       | `NOT NULL`                                | Whether the action is allowed for the role      |

**API Endpoints:**

| Endpoint                 | Method | Description                                  | Request Body (JSON)                                  | Response Body (JSON)                                | Authentication/Authorization |
|--------------------------|--------|----------------------------------------------|------------------------------------------------------|-----------------------------------------------------|------------------------------|
| `/smart-contracts/deploy`| `POST` | Deploy a new smart contract                  | `{ "contract_code": "", "blockchain": "" }`     | `{ "contract_address": "" }`                    | Admin                        |
| `/smart-contracts/interact`| `POST` | Interact with an existing smart contract     | `{ "contract_address": "", "method": "", "args": [] }` | `{ "result": { ... } }`                           | Authenticated User           |
| `/ai/forecast`           | `GET`  | Retrieve AI-driven financial forecasts       | `{ "user_id": "", "data_range": "" }`           | `{ "forecast_data": { ... } }`                    | Authenticated User           |
| `/admin/permissions`     | `PUT`  | Update user role permissions                 | `{ "user_id": "", "role": "", "permissions": [] }` | `{ "status": "success" }`                       | Admin                        |

**Business Logic:**
- Deployment and interaction with smart contracts on BASE/NEAR/Solana.
- Integration with OpenAI + LangChain for AI forecasting, including data ingestion and forecast result storage.
- Implementation of fine-grained Role-Based Access Control (RBAC) using the `access_control_permissions` table.
- Multi-signature support for high-volume cases (Phase 2 feature).

**Supabase Specifics:**
- Use Supabase Edge Functions for interacting with blockchain networks and AI models.
- Implement robust RLS for the `access_control_permissions` table.

## General Considerations for AI Implementation

- **Error Handling:** Implement consistent error handling across all phases as defined in the previous document.
- **Security:** Prioritize security best practices, including input validation, data encryption, and protection against common vulnerabilities.
- **Scalability:** Design for scalability, especially for high-volume transactions and data processing.
- **Testing:** Develop comprehensive test cases for each endpoint and business logic rule.
- **Documentation:** Generate clear and concise documentation for all implemented components, including API specifications and database schemas.

This phase-wise breakdown provides a clear roadmap for an AI to develop the backend of the 360 Creator Bank App, ensuring a systematic and efficient approach to meeting all project requirements.

