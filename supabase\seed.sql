-- Seed data for 360 Creator Bank App
-- This file contains initial data for development and testing

-- Insert sample admin user (will be created through auth, this is just for reference)
-- Note: In production, admin users should be created through proper authentication flow

-- Insert sample access control permissions (additional to those in migration)
INSERT INTO public.access_control_permissions (role, resource, action, allowed) VALUES
-- Additional granular permissions
('creator', 'profile', 'read', true),
('creator', 'profile', 'write', true),
('creator', 'notifications', 'read', true),
('creator', 'referrals', 'read', true),
('creator', 'referrals', 'write', true),

('investor', 'profile', 'read', true),
('investor', 'profile', 'write', true),
('investor', 'notifications', 'read', true),
('investor', 'referrals', 'read', true),
('investor', 'referrals', 'write', true),
('investor', 'creators', 'read', true),

('manager', 'notifications', 'read', true),
('manager', 'notifications', 'write', true),
('manager', 'referrals', 'read', true),

('finance_officer', 'notifications', 'read', true),
('finance_officer', 'reports', 'read', true),
('finance_officer', 'reports', 'write', true)
ON CONFLICT (role, resource, action) DO NOTHING;

-- Insert sample notification templates (for development)
-- These would typically be managed through the application

-- Sample referral codes for testing
-- Note: In production, these would be generated dynamically

-- Sample deal categories and types for reference
-- These could be stored in separate lookup tables if needed

-- Insert sample KYC document types for reference
-- This helps with validation and UI consistency

-- Development-only sample data (should not be used in production)
-- Uncomment the following section only for local development

/*
-- Sample users (for development only)
-- These would normally be created through the authentication flow

-- Note: The following INSERT statements are commented out because
-- users should be created through Supabase Auth, not directly in the database
-- The trigger function handle_new_user() will automatically create
-- corresponding records in the public.users table

-- Sample creators (for development only)
-- INSERT INTO public.creators (user_id, creator_type, primary_platform, platform_username_handle, follower_subscriber_count, monthly_revenue, content_category, years_active_as_creator, bio) VALUES
-- ('sample-creator-uuid', 'Content Creator', 'YouTube', '@samplecreator', '100K - 500K', '$5K - $10K', 'Education', '2-3 years', 'Educational content creator focused on technology and programming');

-- Sample investors (for development only)
-- INSERT INTO public.investors (user_id, investor_type, investment_focus, accreditation_status) VALUES
-- ('sample-investor-uuid', 'Individual', 'Technology, Education', 'Accredited');

-- Sample deals (for development only)
-- INSERT INTO public.deals (creator_id, deal_type, title, description, amount_sought, currency, start_date, end_date, terms, roi_projection, minimum_investment) VALUES
-- ('sample-creator-uuid', 'debt', 'Content Creation Equipment Funding', 'Seeking funding to purchase professional video equipment for content creation', 25000.00, 'USD', NOW(), NOW() + INTERVAL '30 days', '{"interest_rate": 8.5, "term_months": 12}', 8.5, 1000.00);
*/

-- Create indexes for better query performance on commonly accessed data
CREATE INDEX IF NOT EXISTS idx_users_created_at ON public.users(created_at);
CREATE INDEX IF NOT EXISTS idx_deals_created_at ON public.deals(created_at);
CREATE INDEX IF NOT EXISTS idx_investments_created_at ON public.investments(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON public.transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_deals_status_type ON public.deals(status, deal_type);
CREATE INDEX IF NOT EXISTS idx_investments_status_date ON public.investments(status, investment_date);
CREATE INDEX IF NOT EXISTS idx_transactions_user_type_status ON public.transactions(user_id, type, status);
CREATE INDEX IF NOT EXISTS idx_notifications_user_read ON public.notifications(user_id, is_read);

-- Create partial indexes for active records
CREATE INDEX IF NOT EXISTS idx_users_active ON public.users(id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_deals_open ON public.deals(id) WHERE status = 'open';
CREATE INDEX IF NOT EXISTS idx_notifications_unread ON public.notifications(id) WHERE is_read = false;

-- Add helpful comments to tables for documentation
COMMENT ON TABLE public.users IS 'Core user information extending Supabase auth.users';
COMMENT ON TABLE public.creators IS 'Creator-specific profile information and verification status';
COMMENT ON TABLE public.investors IS 'Investor-specific profile information and accreditation details';
COMMENT ON TABLE public.deals IS 'Investment opportunities created by creators';
COMMENT ON TABLE public.investments IS 'Individual investment records linking investors to deals';
COMMENT ON TABLE public.transactions IS 'Financial transaction log for all monetary activities';
COMMENT ON TABLE public.notifications IS 'System notifications for users';
COMMENT ON TABLE public.referrals IS 'Referral tracking system for user acquisition';
COMMENT ON TABLE public.referral_rewards IS 'Rewards earned through successful referrals';
COMMENT ON TABLE public.access_control_permissions IS 'Role-based access control permissions matrix';
COMMENT ON TABLE public.kyc_documents IS 'KYC document storage and verification tracking';

-- Add column comments for important fields
COMMENT ON COLUMN public.users.role IS 'User role determining access permissions and capabilities';
COMMENT ON COLUMN public.users.kyc_status IS 'KYC verification status affecting user capabilities';
COMMENT ON COLUMN public.deals.amount_sought IS 'Total funding amount requested by creator';
COMMENT ON COLUMN public.deals.amount_raised IS 'Current amount of funding received';
COMMENT ON COLUMN public.deals.terms IS 'JSON object containing deal-specific terms and conditions';
COMMENT ON COLUMN public.investments.status IS 'Investment processing status';
COMMENT ON COLUMN public.transactions.reference_id IS 'UUID reference to related entity (deal, investment, etc.)';
COMMENT ON COLUMN public.referrals.referral_code IS 'Unique code used for tracking referrals';

-- Create helpful database functions for common operations
CREATE OR REPLACE FUNCTION public.get_user_stats(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
    user_role user_role;
BEGIN
    SELECT role INTO user_role FROM public.users WHERE id = p_user_id;
    
    IF user_role = 'creator' THEN
        SELECT json_build_object(
            'total_deals', COUNT(d.id),
            'active_deals', COUNT(d.id) FILTER (WHERE d.status = 'open'),
            'total_raised', COALESCE(SUM(d.amount_raised), 0),
            'total_sought', COALESCE(SUM(d.amount_sought), 0)
        ) INTO result
        FROM public.deals d
        WHERE d.creator_id = p_user_id;
        
    ELSIF user_role = 'investor' THEN
        SELECT json_build_object(
            'total_investments', COUNT(i.id),
            'active_investments', COUNT(i.id) FILTER (WHERE i.status = 'completed'),
            'total_invested', COALESCE(SUM(i.amount), 0),
            'portfolio_deals', COUNT(DISTINCT i.deal_id)
        ) INTO result
        FROM public.investments i
        WHERE i.investor_id = p_user_id;
        
    ELSE
        result := json_build_object('message', 'Stats not available for this user role');
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get platform statistics
CREATE OR REPLACE FUNCTION public.get_platform_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_users', (SELECT COUNT(*) FROM public.users WHERE is_active = true),
        'total_creators', (SELECT COUNT(*) FROM public.creators),
        'total_investors', (SELECT COUNT(*) FROM public.investors),
        'total_deals', (SELECT COUNT(*) FROM public.deals),
        'active_deals', (SELECT COUNT(*) FROM public.deals WHERE status = 'open'),
        'total_investments', (SELECT COUNT(*) FROM public.investments),
        'total_volume', (SELECT COALESCE(SUM(amount), 0) FROM public.investments WHERE status = 'completed'),
        'platform_stats_updated', NOW()
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions for the functions
GRANT EXECUTE ON FUNCTION public.get_user_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_platform_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION public.generate_referral_code() TO authenticated;
GRANT EXECUTE ON FUNCTION public.send_notification(UUID, notification_type, TEXT, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION public.calculate_revenue_sharing(NUMERIC) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_full_name(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.can_create_deals(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.can_make_investments(UUID) TO authenticated;
