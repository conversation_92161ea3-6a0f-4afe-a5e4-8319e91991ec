// Document Upload Utility
// Handles secure file uploads for KYC documents

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders, createCorsResponse, createCorsPreflightResponse } from '../_shared/cors.ts'

interface ApiResponse {
  status: 'success' | 'error'
  message: string
  data?: any
  code?: string
  details?: any
}

// Allowed file types for documents
const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
]

// Maximum file size (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024

// Document type mapping
const DOCUMENT_TYPES = {
  'government_id': 'Government ID',
  'proof_of_income': 'Proof of Income',
  'platform_verification': 'Platform Verification',
  'business_documents': 'Business Documents'
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return createCorsPreflightResponse()
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return createCorsResponse({
        status: 'error',
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only POST method is allowed'
      } as ApiResponse, 405)
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return createCorsResponse({
        status: 'error',
        code: 'UNAUTHORIZED',
        message: 'Authorization header is required'
      } as ApiResponse, 401)
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user from JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return createCorsResponse({
        status: 'error',
        code: 'INVALID_TOKEN',
        message: 'Invalid or expired token'
      } as ApiResponse, 401)
    }

    // Parse multipart form data
    const formData = await req.formData()
    const file = formData.get('file') as File
    const documentType = formData.get('document_type') as string

    // Validate required fields
    if (!file) {
      return createCorsResponse({
        status: 'error',
        code: 'VALIDATION_ERROR',
        message: 'File is required'
      } as ApiResponse, 400)
    }

    if (!documentType || !DOCUMENT_TYPES[documentType as keyof typeof DOCUMENT_TYPES]) {
      return createCorsResponse({
        status: 'error',
        code: 'VALIDATION_ERROR',
        message: 'Valid document_type is required',
        details: { valid_types: Object.keys(DOCUMENT_TYPES) }
      } as ApiResponse, 400)
    }

    // Validate file type
    if (!ALLOWED_MIME_TYPES.includes(file.type)) {
      return createCorsResponse({
        status: 'error',
        code: 'INVALID_FILE_TYPE',
        message: 'Invalid file type. Allowed types: images (JPEG, PNG, WebP) and documents (PDF, DOC, DOCX)',
        details: { allowed_types: ALLOWED_MIME_TYPES }
      } as ApiResponse, 400)
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return createCorsResponse({
        status: 'error',
        code: 'FILE_TOO_LARGE',
        message: `File size exceeds maximum limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB`
      } as ApiResponse, 400)
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop() || 'bin'
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const fileName = `${user.id}/${documentType}/${timestamp}.${fileExtension}`

    // Convert file to ArrayBuffer
    const fileBuffer = await file.arrayBuffer()

    // Upload file to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('kyc-documents')
      .upload(fileName, fileBuffer, {
        contentType: file.type,
        metadata: {
          user_id: user.id,
          document_type: documentType,
          original_name: file.name,
          upload_timestamp: new Date().toISOString()
        }
      })

    if (uploadError) {
      console.error('Error uploading file:', uploadError)
      return createCorsResponse({
        status: 'error',
        code: 'UPLOAD_FAILED',
        message: 'Failed to upload file. Please try again.'
      } as ApiResponse, 500)
    }

    // Get public URL for the uploaded file
    const { data: urlData } = supabase.storage
      .from('kyc-documents')
      .getPublicUrl(fileName)

    if (!urlData.publicUrl) {
      console.error('Error getting public URL for uploaded file')
      return createCorsResponse({
        status: 'error',
        code: 'URL_GENERATION_FAILED',
        message: 'File uploaded but failed to generate access URL'
      } as ApiResponse, 500)
    }

    // Create or update document record in database
    const documentRecord = {
      user_id: user.id,
      document_type: documentType,
      document_url: urlData.publicUrl,
      file_name: file.name,
      file_size: file.size,
      mime_type: file.type,
      status: 'pending'
    }

    const { error: dbError } = await supabase
      .from('kyc_documents')
      .upsert(documentRecord, { onConflict: 'user_id,document_type' })

    if (dbError) {
      console.error('Error saving document record:', dbError)
      // Try to clean up uploaded file
      await supabase.storage
        .from('kyc-documents')
        .remove([fileName])
      
      return createCorsResponse({
        status: 'error',
        code: 'DATABASE_ERROR',
        message: 'Failed to save document information'
      } as ApiResponse, 500)
    }

    // Send notification about document upload
    const { error: notificationError } = await supabase
      .rpc('send_notification', {
        p_user_id: user.id,
        p_type: 'in_app',
        p_subject: 'Document Uploaded',
        p_body: `Your ${DOCUMENT_TYPES[documentType as keyof typeof DOCUMENT_TYPES]} has been uploaded successfully and is pending review.`,
        p_metadata: { 
          document_type: documentType,
          file_name: file.name,
          file_size: file.size
        }
      })

    if (notificationError) {
      console.error('Error sending notification:', notificationError)
      // Don't fail the request for notification errors
    }

    // Return success response
    return createCorsResponse({
      status: 'success',
      message: 'Document uploaded successfully',
      data: {
        document_url: urlData.publicUrl,
        document_type: documentType,
        file_name: file.name,
        file_size: file.size,
        mime_type: file.type,
        upload_path: fileName
      }
    } as ApiResponse)

  } catch (error) {
    console.error('Unexpected error in upload-document:', error)
    return createCorsResponse({
      status: 'error',
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred during file upload'
    } as ApiResponse, 500)
  }
})
