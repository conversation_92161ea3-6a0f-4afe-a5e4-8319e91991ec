-- Database Functions and Triggers for Business Logic
-- This migration implements core business logic through PostgreSQL functions and triggers

-- Function to generate unique referral codes
CREATE OR REPLACE FUNCTION public.generate_referral_code()
RETURNS TEXT AS $$
DECLARE
    code TEXT;
    exists BOOLEAN;
BEGIN
    LOOP
        -- Generate a 8-character alphanumeric code
        code := upper(substring(md5(random()::text) from 1 for 8));
        
        -- Check if code already exists
        SELECT EXISTS(SELECT 1 FROM public.referrals WHERE referral_code = code) INTO exists;
        
        -- Exit loop if code is unique
        IF NOT exists THEN
            EXIT;
        END IF;
    END LOOP;
    
    RETURN code;
END;
$$ LANGUAGE plpgsql;

-- Function to validate investment amount against deal constraints
CREATE OR REPLACE FUNCTION public.validate_investment_amount(
    p_deal_id UUID,
    p_amount NUMERIC
)
RETURNS BOOLEAN AS $$
DECLARE
    deal_record RECORD;
    remaining_amount NUMERIC;
BEGIN
    -- Get deal information
    SELECT * INTO deal_record FROM public.deals WHERE id = p_deal_id;
    
    -- Check if deal exists and is open
    IF deal_record IS NULL OR deal_record.status != 'open' THEN
        RETURN FALSE;
    END IF;
    
    -- Calculate remaining amount
    remaining_amount := deal_record.amount_sought - deal_record.amount_raised;
    
    -- Check if investment amount is valid
    IF p_amount <= 0 OR p_amount > remaining_amount THEN
        RETURN FALSE;
    END IF;
    
    -- Check minimum investment constraint
    IF deal_record.minimum_investment IS NOT NULL AND p_amount < deal_record.minimum_investment THEN
        RETURN FALSE;
    END IF;
    
    -- Check maximum investment constraint
    IF deal_record.maximum_investment IS NOT NULL AND p_amount > deal_record.maximum_investment THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to update deal amount raised when investment is made
CREATE OR REPLACE FUNCTION public.update_deal_amount_raised()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update for completed investments
    IF NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
        UPDATE public.deals 
        SET amount_raised = amount_raised + NEW.amount,
            updated_at = NOW()
        WHERE id = NEW.deal_id;
        
        -- Check if deal is now fully funded
        UPDATE public.deals 
        SET status = 'funded',
            updated_at = NOW()
        WHERE id = NEW.deal_id 
        AND amount_raised >= amount_sought;
    END IF;
    
    -- Handle investment cancellation or failure
    IF OLD.status = 'completed' AND NEW.status IN ('cancelled', 'failed') THEN
        UPDATE public.deals 
        SET amount_raised = amount_raised - OLD.amount,
            status = CASE 
                WHEN amount_raised - OLD.amount < amount_sought THEN 'open'
                ELSE status
            END,
            updated_at = NOW()
        WHERE id = NEW.deal_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for updating deal amount raised
CREATE TRIGGER trigger_update_deal_amount_raised
    AFTER UPDATE ON public.investments
    FOR EACH ROW
    EXECUTE FUNCTION public.update_deal_amount_raised();

-- Function to create transaction record for investments
CREATE OR REPLACE FUNCTION public.create_investment_transaction()
RETURNS TRIGGER AS $$
BEGIN
    -- Create transaction record for new investment
    INSERT INTO public.transactions (
        user_id,
        type,
        amount,
        currency,
        status,
        reference_id,
        description,
        metadata
    ) VALUES (
        NEW.investor_id,
        'investment',
        NEW.amount,
        'USD', -- Default currency, should be configurable
        NEW.status::transaction_status,
        NEW.deal_id,
        'Investment in deal: ' || (SELECT title FROM public.deals WHERE id = NEW.deal_id),
        jsonb_build_object('investment_id', NEW.id, 'deal_id', NEW.deal_id)
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for creating investment transactions
CREATE TRIGGER trigger_create_investment_transaction
    AFTER INSERT ON public.investments
    FOR EACH ROW
    EXECUTE FUNCTION public.create_investment_transaction();

-- Function to send notification
CREATE OR REPLACE FUNCTION public.send_notification(
    p_user_id UUID,
    p_type notification_type,
    p_subject TEXT,
    p_body TEXT,
    p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (
        user_id,
        type,
        subject,
        body,
        metadata
    ) VALUES (
        p_user_id,
        p_type,
        p_subject,
        p_body,
        p_metadata
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Function to handle KYC status changes
CREATE OR REPLACE FUNCTION public.handle_kyc_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Send notification when KYC status changes
    IF OLD.kyc_status != NEW.kyc_status THEN
        PERFORM public.send_notification(
            NEW.id,
            'email',
            'KYC Status Update',
            CASE NEW.kyc_status
                WHEN 'approved' THEN 'Your KYC verification has been approved. You can now access all platform features.'
                WHEN 'rejected' THEN 'Your KYC verification has been rejected. Please contact support for more information.'
                WHEN 'under_review' THEN 'Your KYC documents are currently under review. We will notify you once the review is complete.'
                ELSE 'Your KYC status has been updated.'
            END,
            jsonb_build_object('old_status', OLD.kyc_status, 'new_status', NEW.kyc_status)
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for KYC status changes
CREATE TRIGGER trigger_handle_kyc_status_change
    AFTER UPDATE ON public.users
    FOR EACH ROW
    WHEN (OLD.kyc_status IS DISTINCT FROM NEW.kyc_status)
    EXECUTE FUNCTION public.handle_kyc_status_change();

-- Function to handle new deal creation notifications
CREATE OR REPLACE FUNCTION public.handle_new_deal_notification()
RETURNS TRIGGER AS $$
DECLARE
    investor_record RECORD;
BEGIN
    -- Notify all approved investors about new deals
    FOR investor_record IN 
        SELECT u.id, u.email, u.first_name 
        FROM public.users u
        JOIN public.investors i ON u.id = i.user_id
        WHERE u.kyc_status = 'approved' AND u.role = 'investor'
    LOOP
        PERFORM public.send_notification(
            investor_record.id,
            'email',
            'New Investment Opportunity Available',
            'A new ' || NEW.deal_type || ' deal "' || NEW.title || '" is now available for investment.',
            jsonb_build_object('deal_id', NEW.id, 'deal_type', NEW.deal_type, 'deal_title', NEW.title)
        );
    END LOOP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for new deal notifications
CREATE TRIGGER trigger_handle_new_deal_notification
    AFTER INSERT ON public.deals
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_deal_notification();

-- Function to validate referral completion
CREATE OR REPLACE FUNCTION public.validate_referral_completion()
RETURNS TRIGGER AS $$
BEGIN
    -- Mark referral as completed when referred user's KYC is approved
    IF NEW.kyc_status = 'approved' AND (OLD.kyc_status IS NULL OR OLD.kyc_status != 'approved') THEN
        UPDATE public.referrals 
        SET status = 'completed',
            completed_at = NOW(),
            updated_at = NOW()
        WHERE referred_user_id = NEW.id 
        AND status = 'pending';
        
        -- Create referral reward
        INSERT INTO public.referral_rewards (
            referral_id,
            reward_type,
            amount,
            description,
            status
        )
        SELECT 
            r.id,
            'cash',
            50.00, -- Default reward amount
            'Referral bonus for successful user onboarding',
            'pending'
        FROM public.referrals r
        WHERE r.referred_user_id = NEW.id 
        AND r.status = 'completed'
        AND NOT EXISTS (
            SELECT 1 FROM public.referral_rewards rr 
            WHERE rr.referral_id = r.id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for referral completion validation
CREATE TRIGGER trigger_validate_referral_completion
    AFTER UPDATE ON public.users
    FOR EACH ROW
    WHEN (OLD.kyc_status IS DISTINCT FROM NEW.kyc_status)
    EXECUTE FUNCTION public.validate_referral_completion();

-- Function to calculate revenue sharing
CREATE OR REPLACE FUNCTION public.calculate_revenue_sharing(
    p_gross_yield NUMERIC,
    OUT platform_fee NUMERIC,
    OUT creator_return NUMERIC,
    OUT admin_reserve NUMERIC
)
AS $$
BEGIN
    -- Revenue sharing logic as per requirements:
    -- Platform Take: 20% fee
    -- Creator Return: 40%
    -- Admin Reserve: 40%
    
    platform_fee := p_gross_yield * 0.20;
    creator_return := p_gross_yield * 0.40;
    admin_reserve := p_gross_yield * 0.40;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's full name
CREATE OR REPLACE FUNCTION public.get_user_full_name(p_user_id UUID)
RETURNS TEXT AS $$
DECLARE
    full_name TEXT;
BEGIN
    SELECT COALESCE(first_name || ' ' || last_name, email) 
    INTO full_name
    FROM public.users 
    WHERE id = p_user_id;
    
    RETURN COALESCE(full_name, 'Unknown User');
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can create deals (creator with approved KYC)
CREATE OR REPLACE FUNCTION public.can_create_deals(p_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users u
        JOIN public.creators c ON u.id = c.user_id
        WHERE u.id = p_user_id 
        AND u.role = 'creator' 
        AND u.kyc_status = 'approved'
        AND u.is_active = true
    );
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can make investments (investor with approved KYC)
CREATE OR REPLACE FUNCTION public.can_make_investments(p_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users u
        JOIN public.investors i ON u.id = i.user_id
        WHERE u.id = p_user_id 
        AND u.role = 'investor' 
        AND u.kyc_status = 'approved'
        AND u.is_active = true
    );
END;
$$ LANGUAGE plpgsql;

-- Create view for deal summary with creator information
CREATE OR REPLACE VIEW public.deals_with_creator AS
SELECT 
    d.*,
    u.first_name || ' ' || u.last_name as creator_name,
    u.email as creator_email,
    c.primary_platform,
    c.platform_username_handle,
    c.follower_subscriber_count,
    c.content_category,
    (d.amount_raised / d.amount_sought * 100) as funding_percentage
FROM public.deals d
JOIN public.creators c ON d.creator_id = c.user_id
JOIN public.users u ON c.user_id = u.id;

-- Create view for investment summary
CREATE OR REPLACE VIEW public.investments_summary AS
SELECT 
    i.*,
    d.title as deal_title,
    d.deal_type,
    d.status as deal_status,
    u.first_name || ' ' || u.last_name as investor_name,
    u.email as investor_email
FROM public.investments i
JOIN public.deals d ON i.deal_id = d.id
JOIN public.users u ON i.investor_id = u.id;
